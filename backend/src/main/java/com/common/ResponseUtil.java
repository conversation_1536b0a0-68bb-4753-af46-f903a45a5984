package com.common;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;


@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ResponseUtil {
    public static <T> ServiceData<T> serviceData(RetCode retCode, T data, String msg) {
        if (StringUtils.isNotEmpty(msg)) {
            retCode.setMsg(msg);
        }
        ServiceData<T> tServiceData = new ServiceData<>();
        tServiceData.setCode(retCode);
        tServiceData.setBo(data);
        return tServiceData;
    }

    public static <T> ServiceData<T> success(T data, String msg) {
        return serviceData(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID), data, msg);
    }

    public static <T> ServiceData<T> success(T data) {
        return success(data, null);
    }

    public static <T> ServiceData<T> serverError(T data, String msg) {
        return serviceData(new RetCode(RetCode.SERVERERROR_CODE, RetCode.SERVERERROR_MSGID), data, msg);
    }

    public static <T> ServiceData<T> serverError(String msg) {
        return serverError(null, msg);
    }

    public static <T> ServiceData<T> businessError(T data, String msg) {
        return serviceData(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID), data, msg);
    }

    public static <T> ServiceData<T> businessError(String msg) {
        return businessError(null, msg);
    }

    public static <T> ServiceData<T> validateError(T data, String msg) {
        return serviceData(new RetCode(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID), data, msg);
    }

    public static <T> ServiceData<T> validateError(String msg) {
        return validateError(null, msg);
    }

    public static <T> ServiceData<T> authFailed(T data, String msg) {
        return serviceData(new RetCode(RetCode.AUTHFAILED_CODE, RetCode.AUTHFAILED_MSGID), data, msg);
    }

    public static <T> ServiceData<T> authFailed(String msg) {
        return authFailed(null, msg);
    }
}
