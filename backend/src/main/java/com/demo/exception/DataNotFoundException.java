package com.demo.exception;

/**
 * 数据不存在异常
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class DataNotFoundException extends BusinessException {

    private static final long serialVersionUID = 1L;

    public DataNotFoundException() {
        super("4004", "数据不存在");
    }

    public DataNotFoundException(String message) {
        super("4004", message);
    }

    public DataNotFoundException(String message, Throwable cause) {
        super("4004", message, cause);
    }
}
