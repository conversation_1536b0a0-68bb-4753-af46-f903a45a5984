package com.demo.exception;

/**
 * 权限异常
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class UnauthorizedException extends BusinessException {

    private static final long serialVersionUID = 1L;

    public UnauthorizedException() {
        super("4001", "用户未登录或权限不足");
    }

    public UnauthorizedException(String message) {
        super("4001", message);
    }

    public UnauthorizedException(String message, Throwable cause) {
        super("4001", message, cause);
    }
}
