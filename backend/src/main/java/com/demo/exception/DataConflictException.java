package com.demo.exception;

/**
 * 数据冲突异常
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class DataConflictException extends BusinessException {

    private static final long serialVersionUID = 1L;

    public DataConflictException() {
        super("4009", "数据冲突");
    }

    public DataConflictException(String message) {
        super("4009", message);
    }

    public DataConflictException(String message, Throwable cause) {
        super("4009", message, cause);
    }
}
