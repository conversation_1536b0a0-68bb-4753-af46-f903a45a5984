package com.demo.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 餐饮持久化对象
 * 对应数据库表：catering
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                          // ✅ 必须：Lombok数据注解，自动生成getter/setter/toString/equals/hashCode
@TableName("catering")         // ✅ 必须：指定数据库表名
@Builder                       // ✅ 推荐：构建者模式
@AllArgsConstructor           // ✅ 推荐：全参构造函数
@NoArgsConstructor            // ✅ 必须：无参构造函数
public class CateringPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)  // ✅ 必须：主键注解，自增类型
    private Long id;

    /**
     * 餐饮活动标题
     */
    @TableField("title")
    private String title;

    /**
     * 活动原因说明
     */
    @TableField("reason")
    private String reason;

    /**
     * 活动发生时间
     */
    @TableField("occur_time")     // ✅ 必须：使用java.util.Date类型
    private Date occurTime;

    /**
     * 活动地点
     */
    @TableField("location")
    private String location;

    /**
     * 参与人员
     */
    @TableField("participants")
    private String participants;

    // ===== 标准审计字段（必须包含） =====
    
    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_date")   // ✅ 必须：使用java.util.Date类型
    private Date createdDate;

    /**
     * 更新人
     */
    @TableField("last_updated_by")
    private String lastUpdatedBy;

    /**
     * 更新时间
     */
    @TableField("last_updated_date")  // ✅ 必须：使用java.util.Date类型
    private Date lastUpdatedDate;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 有效标识 T：有效，F：无效
     */
    @TableField("enable_flag")
    private String enableFlag;
}