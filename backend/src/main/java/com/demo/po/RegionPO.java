package com.demo.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 地区信息持久化对象
 * 对应数据库表：regions
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                          // ✅ 必须：Lombok数据注解，自动生成getter/setter/toString/equals/hashCode
@TableName("regions")          // ✅ 必须：指定数据库表名
@Builder                       // ✅ 推荐：构建者模式
@AllArgsConstructor           // ✅ 推荐：全参构造函数
@NoArgsConstructor            // ✅ 必须：无参构造函数
public class RegionPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "oid", type = IdType.AUTO)  // ✅ 必须：主键注解，自增类型
    private Long oid;

    /**
     * 地区名称，系统内唯一
     */
    @TableField("region_name")
    private String regionName;

    /**
     * 地区地址
     */
    @TableField("region_address")
    private String regionAddress;

    /**
     * 联系电话
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 地区描述
     */
    @TableField("region_description")
    private String regionDescription;

    /**
     * 排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 地区状态 T：启用，F：禁用
     */
    @TableField("region_status")
    private String regionStatus;

    /**
     * 是否预设地区 T：是，F：否
     */
    @TableField("is_preset")
    private String isPreset;

    // ===== 标准审计字段（必须包含） =====
    
    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_date")   // ✅ 必须：使用java.util.Date类型
    private Date createdDate;

    /**
     * 更新人
     */
    @TableField("last_updated_by")
    private String lastUpdatedBy;

    /**
     * 更新时间
     */
    @TableField("last_updated_date")  // ✅ 必须：使用java.util.Date类型
    private Date lastUpdatedDate;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 有效标识 T：有效，F：无效
     */
    @TableField("enable_flag")
    private String enableFlag;
}
