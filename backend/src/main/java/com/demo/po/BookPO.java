package com.demo.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 图书信息持久化对象
 * 对应数据库表：books
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                          // ✅ 必须：Lombok数据注解，自动生成getter/setter/toString/equals/hashCode
@TableName("books")            // ✅ 必须：指定数据库表名
@Builder                       // ✅ 推荐：构建者模式
@AllArgsConstructor           // ✅ 推荐：全参构造函数
@NoArgsConstructor            // ✅ 必须：无参构造函数
public class BookPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "oid", type = IdType.AUTO)  // ✅ 必须：主键注解，自增类型
    private Long oid;

    /**
     * 图书标题
     */
    @TableField("book_title")
    private String bookTitle;

    /**
     * 图书作者，多个作者用逗号分隔
     */
    @TableField("book_author")
    private String bookAuthor;

    /**
     * ISBN编号，全局唯一
     */
    @TableField("isbn")
    private String isbn;

    /**
     * 分类ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 地区ID
     */
    @TableField("region_id")
    private Long regionId;

    /**
     * 总册数
     */
    @TableField("total_quantity")
    private Integer totalQuantity;

    /**
     * 可借册数
     */
    @TableField("available_quantity")
    private Integer availableQuantity;

    /**
     * 已借册数
     */
    @TableField("borrowed_quantity")
    private Integer borrowedQuantity;

    /**
     * 出版年份
     */
    @TableField("publication_year")
    private Integer publicationYear;

    /**
     * 图书描述
     */
    @TableField("book_description")
    private String bookDescription;

    /**
     * 封面图片URL
     */
    @TableField("cover_image_url")
    private String coverImageUrl;

    /**
     * 图书状态
     */
    @TableField("book_status")
    private String bookStatus;

    // ===== 标准审计字段（必须包含） =====
    
    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_date")   // ✅ 必须：使用java.util.Date类型
    private Date createdDate;

    /**
     * 更新人
     */
    @TableField("last_updated_by")
    private String lastUpdatedBy;

    /**
     * 更新时间
     */
    @TableField("last_updated_date")  // ✅ 必须：使用java.util.Date类型
    private Date lastUpdatedDate;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 有效标识 T：有效，F：无效
     */
    @TableField("enable_flag")
    private String enableFlag;
}
