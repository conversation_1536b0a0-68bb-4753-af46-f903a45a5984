package com.demo.controller;

import com.demo.service.ICategoryService;
import com.demo.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 分类管理控制器
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/api/categories")
@Api(tags = "分类管理")
@Validated
public class CategoryController {

    @Autowired
    private ICategoryService categoryService;

    /**
     * 查询所有分类
     */
    @GetMapping
    @ApiOperation("查询所有分类")
    public ApiResponseVO<CategoryListResultVO> getAllCategories() {
        log.info("查询所有分类列表");
        
        try {
            List<CategoryVO> categoryList = categoryService.getAllCategories();
            CategoryListResultVO result = CategoryListResultVO.of(categoryList);
            return ApiResponseVO.success(result);
            
        } catch (Exception e) {
            log.error("查询分类列表失败", e);
            return ApiResponseVO.systemError("查询分类列表失败");
        }
    }

    /**
     * 根据ID查询分类详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询分类详情")
    public ApiResponseVO<CategoryVO> getCategoryById(
            @ApiParam(value = "分类ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        log.info("根据ID查询分类详情，分类ID：{}", id);
        
        try {
            CategoryVO result = categoryService.getCategoryById(id);
            return ApiResponseVO.success(result);
            
        } catch (Exception e) {
            log.error("查询分类详情失败，分类ID：{}", id, e);
            return ApiResponseVO.businessError("C001", "分类不存在");
        }
    }

    /**
     * 新增分类
     */
    @PostMapping
    @ApiOperation("新增分类")
    public ApiResponseVO<CategoryVO> createCategory(@Valid @RequestBody CategoryCreateVO createVO) {
        log.info("新增分类，创建信息：{}", createVO);
        
        try {
            CategoryVO result = categoryService.createCategory(createVO);
            return ApiResponseVO.success(result);
            
        } catch (Exception e) {
            log.error("新增分类失败，创建信息：{}", createVO, e);
            return ApiResponseVO.businessError("C002", "新增分类失败");
        }
    }

    /**
     * 编辑分类
     */
    @PutMapping("/{id}")
    @ApiOperation("编辑分类")
    public ApiResponseVO<CategoryVO> updateCategory(
            @ApiParam(value = "分类ID", required = true)
            @PathVariable("id") @NotNull Long id,
            @Valid @RequestBody CategoryUpdateVO updateVO) {
        log.info("编辑分类，分类ID：{}，更新信息：{}", id, updateVO);
        
        try {
            updateVO.setOid(id);
            CategoryVO result = categoryService.updateCategory(updateVO);
            return ApiResponseVO.success(result);
            
        } catch (Exception e) {
            log.error("编辑分类失败，分类ID：{}，更新信息：{}", id, updateVO, e);
            return ApiResponseVO.businessError("C003", "编辑分类失败");
        }
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除分类")
    public ApiResponseVO<CategoryDeleteResult> deleteCategory(
            @ApiParam(value = "分类ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        log.info("删除分类，分类ID：{}", id);
        
        try {
            CategoryDeleteResult result = categoryService.deleteCategory(id);
            return ApiResponseVO.success(result);
            
        } catch (Exception e) {
            log.error("删除分类失败，分类ID：{}", id, e);
            return ApiResponseVO.businessError("C004", "删除分类失败");
        }
    }

    /**
     * 切换分类状态
     */
    @PutMapping("/{id}/status")
    @ApiOperation("切换分类状态")
    public ApiResponseVO<CategoryVO> toggleCategoryStatus(
            @ApiParam(value = "分类ID", required = true)
            @PathVariable("id") @NotNull Long id,
            @Valid @RequestBody CategoryStatusVO statusVO) {
        log.info("切换分类状态，分类ID：{}，新状态：{}", id, statusVO.getCategoryStatus());
        
        try {
            CategoryVO result = categoryService.toggleCategoryStatus(id, statusVO.getCategoryStatus());
            return ApiResponseVO.success(result);
            
        } catch (Exception e) {
            log.error("切换分类状态失败，分类ID：{}，新状态：{}", id, statusVO.getCategoryStatus(), e);
            return ApiResponseVO.businessError("C005", "状态切换失败");
        }
    }
}