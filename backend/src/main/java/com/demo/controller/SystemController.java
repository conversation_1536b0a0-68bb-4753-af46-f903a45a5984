package com.demo.controller;

import com.common.ResponseUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.demo.service.ISystemInitService;
import com.demo.vo.SystemInitResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统管理控制器
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/api/system")
@Api(tags = "系统管理")
@Validated
public class SystemController {

    @Autowired
    private ISystemInitService systemInitService;

    /**
     * 系统预设数据初始化
     */
    @PostMapping("/init")
    @ApiOperation("系统预设数据初始化")
    public ServiceData<SystemInitResultVO> initSystemData() {
        log.info("开始系统预设数据初始化");
        
        try {
            SystemInitResultVO result = systemInitService.initSystemData();
            
            if (result.getIsInitialized()) {
                log.info("系统预设数据初始化成功");
                return ResponseUtil.success(result);
            } else {
                log.error("系统预设数据初始化失败：{}", result.getErrorMessage());
                return ResponseUtil.businessError(result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("系统预设数据初始化失败", e);
            SystemInitResultVO errorResult = SystemInitResultVO.builder()
                    .isInitialized(false)
                    .categoryCount(0)
                    .regionCount(0)
                    .errorMessage("系统繁忙，请稍后重试")
                    .build();
            return ResponseUtil.serverError(errorResult, "系统繁忙，请稍后重试");
        }
    }
}
