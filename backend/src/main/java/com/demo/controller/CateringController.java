package com.demo.controller;

import com.common.ResponseUtil;
import com.demo.bo.CateringBO;
import com.demo.service.CateringService;
import com.demo.vo.CateringVO;
import com.zte.itp.msa.core.model.ServiceData;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 餐饮控制器
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Api(value = "餐饮管理", tags = "餐饮管理")
@RestController
@RequestMapping("/api/user/v1/catering")  // ✅ 必须：使用标准路径格式
@Slf4j
@Validated
public class CateringController {

    @Autowired
    private CateringService cateringService;

    @ApiOperation("创建餐饮活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "X-Lang-Id", value = "语言标识", paramType = "header", required = true),
            @ApiImplicitParam(name = "X-Tenant-Id", value = "租户ID", paramType = "header", required = true),
            @ApiImplicitParam(name = "X-Emp-No", value = "登录人账号", paramType = "header", required = true)
    })
    @PostMapping("/create")  // ✅ 必须：使用POST方法创建
    public ServiceData<Long> createCatering(@Valid @RequestBody CateringBO cateringBO,
                                              @RequestHeader("X-Lang-Id") String langId,
                                              @RequestHeader("X-Tenant-Id") String tenantId,
                                              @RequestHeader("X-Emp-No") String empNo) {
        try {
            log.info("创建餐饮活动请求，empNo: {}, tenantId: {}", empNo, tenantId);
            
            Long cateringId = cateringService.createCatering(cateringBO, empNo, tenantId);
            
            return ResponseUtil.success(cateringId);
        } catch (Exception e) {
            log.error("创建餐饮活动失败", e);
            return ResponseUtil.serverError(null, e.getMessage());
        }
    }

    @ApiOperation("更新餐饮活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "X-Lang-Id", value = "语言标识", paramType = "header", required = true),
            @ApiImplicitParam(name = "X-Tenant-Id", value = "租户ID", paramType = "header", required = true),
            @ApiImplicitParam(name = "X-Emp-No", value = "登录人账号", paramType = "header", required = true)
    })
    @PutMapping("/update")  // ✅ 必须：使用PUT方法更新
    public ServiceData<Boolean> updateCatering(@Valid @RequestBody CateringBO cateringBO,
                                              @RequestHeader("X-Lang-Id") String langId,
                                              @RequestHeader("X-Tenant-Id") String tenantId,
                                              @RequestHeader("X-Emp-No") String empNo) {
        try {
            log.info("更新餐饮活动请求，ID: {}, empNo: {}, tenantId: {}", cateringBO.getId(), empNo, tenantId);
            
            Boolean result = cateringService.updateCatering(cateringBO, empNo, tenantId);
            
            return ResponseUtil.success(result);
        } catch (Exception e) {
            log.error("更新餐饮活动失败", e);
            return ResponseUtil.serverError(null, e.getMessage());
        }
    }

    @ApiOperation("删除餐饮活动（逻辑删除）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "X-Lang-Id", value = "语言标识", paramType = "header", required = true),
            @ApiImplicitParam(name = "X-Tenant-Id", value = "租户ID", paramType = "header", required = true),
            @ApiImplicitParam(name = "X-Emp-No", value = "登录人账号", paramType = "header", required = true)
    })
    @DeleteMapping("/delete")  // ✅ 必须：使用DELETE方法删除（逻辑删除）
    public ServiceData<Boolean> deleteCatering(@RequestParam @NotNull Long id,
                                              @RequestHeader("X-Lang-Id") String langId,
                                              @RequestHeader("X-Tenant-Id") String tenantId,
                                              @RequestHeader("X-Emp-No") String empNo) {
        try {
            log.info("删除餐饮活动请求，ID: {}, empNo: {}, tenantId: {}", id, empNo, tenantId);
            
            Boolean result = cateringService.deleteCatering(id, empNo, tenantId);
            
            return ResponseUtil.success(result);
        } catch (Exception e) {
            log.error("删除餐饮活动失败", e);
            return ResponseUtil.serverError(null, e.getMessage());
        }
    }

    @ApiOperation("批量删除餐饮活动（逻辑删除）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "X-Lang-Id", value = "语言标识", paramType = "header", required = true),
            @ApiImplicitParam(name = "X-Tenant-Id", value = "租户ID", paramType = "header", required = true),
            @ApiImplicitParam(name = "X-Emp-No", value = "登录人账号", paramType = "header", required = true)
    })
    @DeleteMapping("/batch-delete")
    public ServiceData<Integer> batchDeleteCatering(@RequestBody List<Long> ids,
                                                   @RequestHeader("X-Lang-Id") String langId,
                                                   @RequestHeader("X-Tenant-Id") String tenantId,
                                                   @RequestHeader("X-Emp-No") String empNo) {
        try {
            log.info("批量删除餐饮活动请求，数量: {}, empNo: {}, tenantId: {}", ids.size(), empNo, tenantId);
            
            Integer result = cateringService.batchDeleteCatering(ids, empNo, tenantId);
            
            return ResponseUtil.success(result);
        } catch (Exception e) {
            log.error("批量删除餐饮活动失败", e);
            return ResponseUtil.serverError(null, e.getMessage());
        }
    }

    @ApiOperation("根据ID查询餐饮活动详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "X-Lang-Id", value = "语言标识", paramType = "header", required = true),
            @ApiImplicitParam(name = "X-Tenant-Id", value = "租户ID", paramType = "header", required = true)
    })
    @GetMapping("/{id}")  // ✅ 必须：使用GET方法查询
    public ServiceData<CateringVO> getCateringById(@PathVariable @NotNull Long id,
                                               @RequestHeader("X-Lang-Id") String langId,
                                               @RequestHeader("X-Tenant-Id") String tenantId) {
        try {
            log.info("查询餐饮活动详情，ID: {}, tenantId: {}", id, tenantId);
            
            boolean isEnglish = "en-US".equals(langId);
            CateringVO result = cateringService.getCateringById(id, tenantId, isEnglish);
            
            return ResponseUtil.success(result);
        } catch (Exception e) {
            log.error("查询餐饮活动详情失败", e);
            return ResponseUtil.serverError(null, e.getMessage());
        }
    }

    @ApiOperation("分页查询餐饮活动列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "X-Lang-Id", value = "语言标识", paramType = "header", required = true),
            @ApiImplicitParam(name = "X-Tenant-Id", value = "租户ID", paramType = "header", required = true)
    })
    @GetMapping("/list")  // ✅ 必须：使用GET方法查询列表，支持分页
    public ServiceData<Map<String, Object>> queryCateringPage(@RequestParam(defaultValue = "1") @Min(1) Integer pageNum,
                                                 @RequestParam(defaultValue = "10") @Min(1) Integer pageSize,
                                                 @RequestHeader("X-Lang-Id") String langId,
                                                 @RequestHeader("X-Tenant-Id") String tenantId) {
        try {
            log.info("分页查询餐饮活动，pageNum: {}, pageSize: {}, tenantId: {}", pageNum, pageSize, tenantId);
            
            boolean isEnglish = "en-US".equals(langId);
            List<CateringVO> list = cateringService.queryCateringPage(tenantId, pageNum, pageSize, isEnglish);
            Integer total = cateringService.countCatering(tenantId);
            
            // ✅ 必须：标准分页响应格式
            Map<String, Object> pageResult = new HashMap<>();
            pageResult.put("list", list);
            pageResult.put("total", total);
            pageResult.put("pageNum", pageNum);
            pageResult.put("pageSize", pageSize);
            pageResult.put("pages", (total + pageSize - 1) / pageSize);
            
            return ResponseUtil.success(pageResult);
        } catch (Exception e) {
            log.error("分页查询餐饮活动失败", e);
            return ResponseUtil.serverError(null, e.getMessage());
        }
    }

    @ApiOperation("根据标题搜索餐饮活动")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "X-Lang-Id", value = "语言标识", paramType = "header", required = true),
            @ApiImplicitParam(name = "X-Tenant-Id", value = "租户ID", paramType = "header", required = true)
    })
    @GetMapping("/search")
    public ServiceData<List<CateringVO>> searchCateringByTitle(@RequestParam String title,
                                                     @RequestHeader("X-Lang-Id") String langId,
                                                     @RequestHeader("X-Tenant-Id") String tenantId) {
        try {
            log.info("根据标题搜索餐饮活动，title: {}, tenantId: {}", title, tenantId);
            
            boolean isEnglish = "en-US".equals(langId);
            List<CateringVO> result = cateringService.queryCateringByTitle(title, tenantId, isEnglish);
            
            return ResponseUtil.success(result);
        } catch (Exception e) {
            log.error("根据标题搜索餐饮活动失败", e);
            return ResponseUtil.serverError(null, e.getMessage());
        }
    }
}