package com.demo.controller;

import com.demo.service.IBookService;
import com.demo.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 图书管理控制器
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/api/books")
@Api(tags = "图书管理")
@Validated
public class BookController {

    @Autowired
    private IBookService bookService;

    /**
     * 图书基础查询
     */
    @PostMapping("/query/list")
    @ApiOperation("图书基础查询")
    public ApiResponseVO<PageResultVO<BookVO>> queryBookList(@Valid @RequestBody BookQueryVO queryVO) {
        log.info("分页查询图书列表，查询条件：{}", queryVO);
        
        try {
            PageResultVO<BookVO> result = bookService.queryBookList(queryVO);
            return ApiResponseVO.success(result);
            
        } catch (Exception e) {
            log.error("查询图书列表失败，查询条件：{}", queryVO, e);
            return ApiResponseVO.systemError("系统繁忙，请稍后重试");
        }
    }

    /**
     * 图书详情查看
     */
    @GetMapping("/{id}")
    @ApiOperation("图书详情查看")
    public ApiResponseVO<BookVO> getBookById(
            @ApiParam(value = "图书ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        log.info("获取图书详情，图书ID：{}", id);
        
        try {
            BookVO result = bookService.getBookById(id);
            return ApiResponseVO.success(result);
            
        } catch (Exception e) {
            log.error("获取图书详情失败，图书ID：{}", id, e);
            return ApiResponseVO.businessError("B001", "图书不存在");
        }
    }

    /**
     * 图书新增
     */
    @PostMapping
    @ApiOperation("图书新增")
    public ApiResponseVO<BookCreateResult> createBook(@Valid @RequestBody BookCreateVO createVO) {
        log.info("新增图书，创建信息：{}", createVO);
        
        try {
            BookVO bookVO = bookService.createBook(createVO);
            
            BookCreateResult result = BookCreateResult.builder()
                    .oid(bookVO.getOid())
                    .bookTitle(bookVO.getBookTitle())
                    .isbn(bookVO.getIsbn())
                    .isNewRecord(true)
                    .build();
            
            return ApiResponseVO.success(result);
            
        } catch (Exception e) {
            log.error("新增图书失败，创建信息：{}", createVO, e);
            return ApiResponseVO.businessError("B002", "新增图书失败");
        }
    }

    /**
     * 图书编辑
     */
    @PutMapping("/{id}")
    @ApiOperation("图书编辑")
    public ApiResponseVO<BookVO> updateBook(
            @ApiParam(value = "图书ID", required = true)
            @PathVariable("id") @NotNull Long id,
            @Valid @RequestBody BookUpdateVO updateVO) {
        log.info("编辑图书，图书ID：{}，更新信息：{}", id, updateVO);
        
        try {
            updateVO.setOid(id);
            BookVO result = bookService.updateBook(updateVO);
            return ApiResponseVO.success(result);
            
        } catch (Exception e) {
            log.error("编辑图书失败，图书ID：{}，更新信息：{}", id, updateVO, e);
            return ApiResponseVO.businessError("B003", "编辑图书失败");
        }
    }

    /**
     * 图书删除
     */
    @DeleteMapping("/{id}")
    @ApiOperation("图书删除")
    public ApiResponseVO<BookDeleteResult> deleteBook(
            @ApiParam(value = "图书ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        log.info("删除图书，图书ID：{}", id);
        
        try {
            BookDeleteResult result = bookService.deleteBook(id);
            return ApiResponseVO.success(result);
            
        } catch (Exception e) {
            log.error("删除图书失败，图书ID：{}", id, e);
            return ApiResponseVO.businessError("B004", "删除图书失败");
        }
    }

    /**
     * 图书批量删除
     */
    @DeleteMapping("/batch")
    @ApiOperation("图书批量删除")
    public ApiResponseVO<BookBatchDeleteResult> batchDeleteBooks(@Valid @RequestBody BookBatchDeleteVO batchDeleteVO) {
        log.info("批量删除图书，图书ID列表：{}", batchDeleteVO.getBookIds());
        
        try {
            BookBatchDeleteResult result = bookService.batchDeleteBooks(batchDeleteVO.getBookIds());
            return ApiResponseVO.success(result);
            
        } catch (Exception e) {
            log.error("批量删除图书失败，图书ID列表：{}", batchDeleteVO.getBookIds(), e);
            return ApiResponseVO.businessError("B005", "批量删除图书失败");
        }
    }

    /**
     * 图书新增结果
     */
    public static class BookCreateResult {
        private Long oid;
        private String bookTitle;
        private String isbn;
        private Boolean isNewRecord;

        public static BookCreateResultBuilder builder() {
            return new BookCreateResultBuilder();
        }

        public Long getOid() {
            return oid;
        }

        public void setOid(Long oid) {
            this.oid = oid;
        }

        public String getBookTitle() {
            return bookTitle;
        }

        public void setBookTitle(String bookTitle) {
            this.bookTitle = bookTitle;
        }

        public String getIsbn() {
            return isbn;
        }

        public void setIsbn(String isbn) {
            this.isbn = isbn;
        }

        public Boolean getIsNewRecord() {
            return isNewRecord;
        }

        public void setIsNewRecord(Boolean isNewRecord) {
            this.isNewRecord = isNewRecord;
        }

        public static class BookCreateResultBuilder {
            private Long oid;
            private String bookTitle;
            private String isbn;
            private Boolean isNewRecord;

            public BookCreateResultBuilder oid(Long oid) {
                this.oid = oid;
                return this;
            }

            public BookCreateResultBuilder bookTitle(String bookTitle) {
                this.bookTitle = bookTitle;
                return this;
            }

            public BookCreateResultBuilder isbn(String isbn) {
                this.isbn = isbn;
                return this;
            }

            public BookCreateResultBuilder isNewRecord(Boolean isNewRecord) {
                this.isNewRecord = isNewRecord;
                return this;
            }

            public BookCreateResult build() {
                BookCreateResult result = new BookCreateResult();
                result.setOid(this.oid);
                result.setBookTitle(this.bookTitle);
                result.setIsbn(this.isbn);
                result.setIsNewRecord(this.isNewRecord);
                return result;
            }
        }
    }
}
