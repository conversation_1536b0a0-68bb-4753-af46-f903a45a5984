package com.demo.controller;

import com.common.ResponseUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.demo.service.IFileUploadService;
import com.demo.vo.FileUploadResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传控制器
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/api/upload")
@Api(tags = "文件上传")
@Validated
public class FileUploadController {

    @Autowired
    private IFileUploadService fileUploadService;

    /**
     * 图书封面上传
     */
    @PostMapping("/book-cover")
    @ApiOperation("图书封面上传")
    public ServiceData<FileUploadResultVO> uploadBookCover(
            @ApiParam(value = "图片文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "图书ID", required = false) @RequestParam(value = "bookId", required = false) Long bookId) {
        
        log.info("开始上传图书封面，文件名：{}，图书ID：{}", 
                file != null ? file.getOriginalFilename() : "null", bookId);
        
        try {
            FileUploadResultVO result = fileUploadService.uploadBookCover(file, bookId);
            return ResponseUtil.success(result);
            
        } catch (Exception e) {
            log.error("图书封面上传失败，文件名：{}", 
                    file != null ? file.getOriginalFilename() : "null", e);
            return ResponseUtil.businessError("文件上传失败");
        }
    }
}
