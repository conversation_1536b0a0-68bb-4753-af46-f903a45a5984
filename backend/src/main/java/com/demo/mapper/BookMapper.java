package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demo.po.BookPO;
import com.demo.vo.BookQueryVO;
import com.demo.vo.BookVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图书信息数据访问接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface BookMapper extends BaseMapper<BookPO> {

    /**
     * 条件查询图书列表
     * 
     * @param page 分页对象
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<BookVO> selectBookListWithCondition(IPage<BookVO> page, @Param("query") BookQueryVO queryVO);

    /**
     * 根据图书ID获取图书详情
     * 
     * @param bookId 图书ID
     * @return 图书详情
     */
    BookVO selectBookDetailById(@Param("bookId") Long bookId);

    /**
     * 检查ISBN是否存在
     * 
     * @param isbn ISBN编号
     * @param excludeId 排除的图书ID（用于编辑时检查）
     * @return 存在的记录数
     */
    Integer checkIsbnExists(@Param("isbn") String isbn, @Param("excludeId") Long excludeId);

    /**
     * 根据分类ID统计图书数量
     * 
     * @param categoryId 分类ID
     * @return 图书数量
     */
    Integer countBooksByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 根据地区ID统计图书数量
     * 
     * @param regionId 地区ID
     * @return 图书数量
     */
    Integer countBooksByRegionId(@Param("regionId") Long regionId);

    /**
     * 批量删除图书
     * 
     * @param bookIds 图书ID列表
     * @return 影响行数
     */
    Integer batchDeleteBooks(@Param("bookIds") List<Long> bookIds);
}
