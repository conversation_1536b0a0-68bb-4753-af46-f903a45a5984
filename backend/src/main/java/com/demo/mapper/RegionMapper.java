package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demo.po.RegionPO;
import com.demo.vo.RegionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 地区信息数据访问接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface RegionMapper extends BaseMapper<RegionPO> {

    /**
     * 查询所有地区列表（带图书数量统计）
     * 
     * @return 地区列表
     */
    List<RegionVO> selectRegionListWithBookCount();

    /**
     * 批量查询地区名称
     * 
     * @param regionIds 地区ID列表
     * @return 地区ID和名称的映射
     */
    Map<Long, String> batchQueryRegionNames(@Param("regionIds") List<Long> regionIds);

    /**
     * 检查地区名称是否存在
     * 
     * @param regionName 地区名称
     * @param excludeId 排除的地区ID（用于编辑时检查）
     * @return 存在的记录数
     */
    Integer checkRegionNameExists(@Param("regionName") String regionName, 
                                 @Param("excludeId") Long excludeId);

    /**
     * 获取地区关联的图书数量
     * 
     * @param regionId 地区ID
     * @return 关联图书数量
     */
    Integer getBookCountByRegionId(@Param("regionId") Long regionId);
}
