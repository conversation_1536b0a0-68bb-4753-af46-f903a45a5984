package com.demo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demo.po.CateringPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 餐饮数据访问层
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface CateringMapper extends BaseMapper<CateringPO> {

    /**
     * 根据租户ID分页查询有效记录
     * 
     * @param tenantId 租户ID
     * @param enableFlag 有效标识
     * @param offset 偏移量
     * @param pageSize 页大小
     * @return 餐饮活动列表
     */
    List<CateringPO> selectPageByTenant(@Param("tenantId") String tenantId, 
                                        @Param("enableFlag") String enableFlag,
                                        @Param("offset") Integer offset, 
                                        @Param("pageSize") Integer pageSize);

    /**
     * 根据租户ID统计有效记录数
     * 
     * @param tenantId 租户ID
     * @param enableFlag 有效标识
     * @return 总记录数
     */
    Integer countByTenant(@Param("tenantId") String tenantId, 
                         @Param("enableFlag") String enableFlag);

    /**
     * 根据标题模糊查询
     * 
     * @param title 标题关键词
     * @param tenantId 租户ID
     * @param enableFlag 有效标识
     * @return 餐饮活动列表
     */
    List<CateringPO> selectByTitleLike(@Param("title") String title,
                                       @Param("tenantId") String tenantId, 
                                       @Param("enableFlag") String enableFlag);

    /**
     * 批量逻辑删除
     * 
     * @param ids 主键ID列表
     * @param lastUpdatedBy 更新人
     * @param tenantId 租户ID
     * @return 更新记录数
     */
    Integer batchLogicDelete(@Param("ids") List<Long> ids,
                            @Param("lastUpdatedBy") String lastUpdatedBy,
                            @Param("tenantId") String tenantId);
}