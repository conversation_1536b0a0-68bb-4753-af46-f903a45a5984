package com.demo.service;

import com.demo.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 分类信息业务逻辑接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface ICategoryService {

    /**
     * 查询所有分类列表
     * 
     * @return 分类列表
     */
    List<CategoryVO> getAllCategories();

    /**
     * 批量查询分类名称
     * 
     * @param categoryIds 分类ID列表
     * @return 分类ID与名称的映射
     */
    Map<Long, String> batchQueryCategoryNames(List<Long> categoryIds);

    /**
     * 根据分类ID获取分类信息
     * 
     * @param categoryId 分类ID
     * @return 分类信息
     */
    CategoryVO getCategoryById(Long categoryId);

    /**
     * 新增分类
     * 
     * @param createVO 创建信息
     * @return 分类信息
     */
    CategoryVO createCategory(CategoryCreateVO createVO);

    /**
     * 编辑分类
     * 
     * @param updateVO 更新信息
     * @return 分类信息
     */
    CategoryVO updateCategory(CategoryUpdateVO updateVO);

    /**
     * 删除分类
     * 
     * @param categoryId 分类ID
     * @return 删除结果
     */
    CategoryDeleteResult deleteCategory(Long categoryId);

    /**
     * 切换分类状态
     * 
     * @param categoryId 分类ID
     * @param status 新状态
     * @return 分类信息
     */
    CategoryVO toggleCategoryStatus(Long categoryId, String status);

    /**
     * 检查分类名称是否存在
     * 
     * @param categoryName 分类名称
     * @param excludeId 排除的分类ID（用于编辑时检查）
     * @return true：存在，false：不存在
     */
    Boolean checkCategoryNameExists(String categoryName, Long excludeId);

    /**
     * 获取分类关联的图书数量
     * 
     * @param categoryId 分类ID
     * @return 图书数量
     */
    Integer getBookCountByCategoryId(Long categoryId);
}