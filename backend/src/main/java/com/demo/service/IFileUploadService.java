package com.demo.service;

import com.demo.vo.FileUploadResultVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传业务逻辑接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IFileUploadService {

    /**
     * 上传图书封面
     * 
     * @param file 图片文件
     * @param bookId 图书ID（可选）
     * @return 上传结果
     */
    FileUploadResultVO uploadBookCover(MultipartFile file, Long bookId);
}
