package com.demo.service;

import com.demo.vo.RegionVO;

import java.util.List;
import java.util.Map;

/**
 * 地区信息业务逻辑接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IRegionService {

    /**
     * 查询所有地区列表
     * 
     * @return 地区列表
     */
    List<RegionVO> getAllRegions();

    /**
     * 批量查询地区名称
     * 
     * @param regionIds 地区ID列表
     * @return 地区ID和名称的映射
     */
    Map<Long, String> batchQueryRegionNames(List<Long> regionIds);

    /**
     * 根据地区ID获取地区信息
     * 
     * @param regionId 地区ID
     * @return 地区信息
     */
    RegionVO getRegionById(Long regionId);

    /**
     * 检查地区名称是否存在
     * 
     * @param regionName 地区名称
     * @param excludeId 排除的地区ID（用于编辑时检查）
     * @return true：存在，false：不存在
     */
    Boolean checkRegionNameExists(String regionName, Long excludeId);

    /**
     * 获取地区关联的图书数量
     * 
     * @param regionId 地区ID
     * @return 关联图书数量
     */
    Integer getBookCountByRegionId(Long regionId);
}
