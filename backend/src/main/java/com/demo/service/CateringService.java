package com.demo.service;

import com.demo.bo.CateringBO;
import com.demo.vo.CateringVO;

import java.util.List;

/**
 * 餐饮服务接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface CateringService {

    /**
     * 创建餐饮活动
     * 
     * @param cateringBO 餐饮业务对象
     * @param currentUser 当前用户
     * @param tenantId 租户ID
     * @return 创建成功的餐饮活动ID
     */
    Long createCatering(CateringBO cateringBO, String currentUser, String tenantId);

    /**
     * 更新餐饮活动
     * 
     * @param cateringBO 餐饮业务对象
     * @param currentUser 当前用户
     * @param tenantId 租户ID
     * @return 是否更新成功
     */
    Boolean updateCatering(CateringBO cateringBO, String currentUser, String tenantId);

    /**
     * 逻辑删除餐饮活动
     * 
     * @param id 主键ID
     * @param currentUser 当前用户
     * @param tenantId 租户ID
     * @return 是否删除成功
     */
    Boolean deleteCatering(Long id, String currentUser, String tenantId);

    /**
     * 批量逻辑删除餐饮活动
     * 
     * @param ids 主键ID列表
     * @param currentUser 当前用户
     * @param tenantId 租户ID
     * @return 删除成功的数量
     */
    Integer batchDeleteCatering(List<Long> ids, String currentUser, String tenantId);

    /**
     * 根据ID查询餐饮活动
     * 
     * @param id 主键ID
     * @param tenantId 租户ID
     * @param isEnglish 是否英文环境
     * @return 餐饮活动VO对象
     */
    CateringVO getCateringById(Long id, String tenantId, boolean isEnglish);

    /**
     * 分页查询餐饮活动列表
     * 
     * @param tenantId 租户ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param isEnglish 是否英文环境
     * @return 餐饮活动VO列表
     */
    List<CateringVO> queryCateringPage(String tenantId, Integer pageNum, Integer pageSize, boolean isEnglish);

    /**
     * 统计餐饮活动总数
     * 
     * @param tenantId 租户ID
     * @return 总记录数
     */
    Integer countCatering(String tenantId);

    /**
     * 根据标题模糊查询餐饮活动
     * 
     * @param title 标题关键词
     * @param tenantId 租户ID
     * @param isEnglish 是否英文环境
     * @return 餐饮活动VO列表
     */
    List<CateringVO> queryCateringByTitle(String title, String tenantId, boolean isEnglish);
}