package com.demo.service;

import com.demo.vo.*;

import java.util.List;

/**
 * 图书信息业务逻辑接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IBookService {

    /**
     * 分页查询图书列表
     * 
     * @param queryVO 查询条件
     * @return 分页结果
     */
    PageResultVO<BookVO> queryBookList(BookQueryVO queryVO);

    /**
     * 根据图书ID获取图书详情
     * 
     * @param bookId 图书ID
     * @return 图书详情
     */
    BookVO getBookById(Long bookId);

    /**
     * 新增图书
     * 
     * @param createVO 创建信息
     * @return 创建结果
     */
    BookVO createBook(BookCreateVO createVO);

    /**
     * 编辑图书
     * 
     * @param updateVO 更新信息
     * @return 更新结果
     */
    BookVO updateBook(BookUpdateVO updateVO);

    /**
     * 删除图书
     * 
     * @param bookId 图书ID
     * @return 删除结果
     */
    BookDeleteResult deleteBook(Long bookId);

    /**
     * 批量删除图书
     * 
     * @param bookIds 图书ID列表
     * @return 批量删除结果
     */
    BookBatchDeleteResult batchDeleteBooks(List<Long> bookIds);

    /**
     * 检查ISBN是否存在
     * 
     * @param isbn ISBN编号
     * @param excludeId 排除的图书ID（用于编辑时检查）
     * @return 是否存在
     */
    Boolean checkIsbnExists(String isbn, Long excludeId);
}