package com.demo.service.impl;

import com.demo.mapper.CategoryMapper;
import com.demo.mapper.RegionMapper;
import com.demo.po.CategoryPO;
import com.demo.po.RegionPO;
import com.demo.service.ISystemInitService;
import com.demo.vo.SystemInitResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 系统初始化业务逻辑实现
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
@Transactional
public class SystemInitServiceImpl implements ISystemInitService {

    @Autowired
    private CategoryMapper categoryMapper;

    @Autowired
    private RegionMapper regionMapper;

    @Override
    @Transactional
    public SystemInitResultVO initSystemData() {
        log.info("开始系统预设数据初始化");
        
        try {
            // 检查是否已经初始化过
            if (isAlreadyInitialized()) {
                log.info("系统已经初始化过，跳过初始化过程");
                return SystemInitResultVO.builder()
                        .isInitialized(true)
                        .categoryCount(8)
                        .regionCount(3)
                        .errorMessage(null)
                        .build();
            }
            
            // 初始化分类数据
            int categoryCount = initCategoryData();
            
            // 初始化地区数据
            int regionCount = initRegionData();
            
            log.info("系统预设数据初始化完成，分类数量：{}，地区数量：{}", categoryCount, regionCount);
            
            return SystemInitResultVO.builder()
                    .isInitialized(true)
                    .categoryCount(categoryCount)
                    .regionCount(regionCount)
                    .errorMessage(null)
                    .build();
                    
        } catch (Exception e) {
            log.error("系统预设数据初始化失败", e);
            return SystemInitResultVO.builder()
                    .isInitialized(false)
                    .categoryCount(0)
                    .regionCount(0)
                    .errorMessage(e.getMessage())
                    .build();
        }
    }

    /**
     * 检查是否已经初始化过
     * 
     * @return true：已初始化，false：未初始化
     */
    private boolean isAlreadyInitialized() {
        // 检查是否已存在预设分类
        Long categoryCountLong = categoryMapper.selectCount(null);
        Long regionCountLong = regionMapper.selectCount(null);
        
        Integer categoryCount = categoryCountLong != null ? categoryCountLong.intValue() : 0;
        Integer regionCount = regionCountLong != null ? regionCountLong.intValue() : 0;
        
        return categoryCount != null && categoryCount > 0 && regionCount != null && regionCount > 0;
    }

    /**
     * 初始化分类数据
     * 
     * @return 创建的分类数量
     */
    private int initCategoryData() {
        log.info("开始初始化预设分类数据");
        
        List<CategoryPO> categoryList = new ArrayList<>();
        Date now = new Date();
        
        // 创建8个预设图书分类
        categoryList.add(buildCategoryPO("文学艺术", "文学、艺术、美学类图书", 1, now));
        categoryList.add(buildCategoryPO("科学技术", "科学、技术、工程类图书", 2, now));
        categoryList.add(buildCategoryPO("社会科学", "社会科学、人文科学类图书", 3, now));
        categoryList.add(buildCategoryPO("经济管理", "经济学、管理学类图书", 4, now));
        categoryList.add(buildCategoryPO("历史地理", "历史、地理、传记类图书", 5, now));
        categoryList.add(buildCategoryPO("医学健康", "医学、健康、养生类图书", 6, now));
        categoryList.add(buildCategoryPO("教育心理", "教育学、心理学类图书", 7, now));
        categoryList.add(buildCategoryPO("工具参考", "词典、手册、参考书类", 8, now));
        
        // 批量插入
        for (CategoryPO category : categoryList) {
            categoryMapper.insert(category);
        }
        
        log.info("预设分类数据初始化完成，共创建{}个分类", categoryList.size());
        return categoryList.size();
    }

    /**
     * 初始化地区数据
     * 
     * @return 创建的地区数量
     */
    private int initRegionData() {
        log.info("开始初始化预设地区数据");
        
        List<RegionPO> regionList = new ArrayList<>();
        Date now = new Date();
        
        // 创建3个预设地区
        regionList.add(buildRegionPO("总馆", "北京市海淀区中关村南大街1号", "010-12345678", 
                "图书管理系统总馆，负责全系统的统一管理", 1, now));
        regionList.add(buildRegionPO("上海分馆", "上海市浦东新区张江高科技园区", "021-87654321", 
                "上海地区分馆，服务上海及华东地区", 2, now));
        regionList.add(buildRegionPO("深圳分馆", "深圳市南山区高新技术产业园区", "0755-98765432", 
                "深圳地区分馆，服务深圳及华南地区", 3, now));
        
        // 批量插入
        for (RegionPO region : regionList) {
            regionMapper.insert(region);
        }
        
        log.info("预设地区数据初始化完成，共创建{}个地区", regionList.size());
        return regionList.size();
    }

    /**
     * 构建分类PO对象
     */
    private CategoryPO buildCategoryPO(String name, String description, int sortOrder, Date now) {
        return CategoryPO.builder()
                .categoryName(name)
                .categoryDescription(description)
                .sortOrder(sortOrder)
                .categoryStatus("T")
                .isPreset("T")
                .createdBy("system")
                .createdDate(now)
                .lastUpdatedBy("system")
                .lastUpdatedDate(now)
                .tenantId("10001")
                .enableFlag("T")
                .build();
    }

    /**
     * 构建地区PO对象
     */
    private RegionPO buildRegionPO(String name, String address, String phone, String description, 
                                  int sortOrder, Date now) {
        return RegionPO.builder()
                .regionName(name)
                .regionAddress(address)
                .contactPhone(phone)
                .regionDescription(description)
                .sortOrder(sortOrder)
                .regionStatus("T")
                .isPreset("T")
                .createdBy("system")
                .createdDate(now)
                .lastUpdatedBy("system")
                .lastUpdatedDate(now)
                .tenantId("10001")
                .enableFlag("T")
                .build();
    }
}
