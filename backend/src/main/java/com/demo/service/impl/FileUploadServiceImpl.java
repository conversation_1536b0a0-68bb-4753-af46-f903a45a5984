package com.demo.service.impl;

import com.demo.exception.BusinessException;
import com.demo.service.IFileUploadService;
import com.demo.vo.FileUploadResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.UUID;

/**
 * 文件上传业务逻辑实现
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
public class FileUploadServiceImpl implements IFileUploadService {

    @Value("${file.upload.path:/tmp/uploads}")
    private String uploadPath;
    
    @Value("${file.upload.domain:http://localhost:8080}")
    private String uploadDomain;

    private static final long MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
    private static final String[] ALLOWED_TYPES = {"image/jpeg", "image/jpg", "image/png"};
    private static final int MIN_WIDTH = 100;
    private static final int MIN_HEIGHT = 100;
    private static final int MAX_WIDTH = 800;
    private static final int MAX_HEIGHT = 800;

    @Override
    public FileUploadResultVO uploadBookCover(MultipartFile file, Long bookId) {
        log.info("开始上传图书封面，文件名：{}，图书ID：{}", 
                file.getOriginalFilename(), bookId);
        
        try {
            // 1. 文件校验
            validateBookCoverFile(file);
            
            // 2. 生成文件名
            String fileName = generateFileName(file.getOriginalFilename());
            
            // 3. 保存文件
            String filePath = saveFile(file, fileName);
            
            // 4. 生成访问URL
            String fileUrl = uploadDomain + "/images/books/" + fileName;
            
            // 5. 返回结果
            FileUploadResultVO result = FileUploadResultVO.builder()
                    .fileUrl(fileUrl)
                    .fileName(fileName)
                    .fileSize(file.getSize())
                    .uploadTime(formatDateTime(new Date()))
                    .build();
            
            log.info("图书封面上传成功，文件URL：{}", fileUrl);
            return result;
            
        } catch (Exception e) {
            log.error("图书封面上传失败，文件名：{}", file.getOriginalFilename(), e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException("FILE_UPLOAD_ERROR");
        }
    }

    /**
     * 校验图书封面文件
     * 
     * @param file 文件
     */
    private void validateBookCoverFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("FILE_EMPTY");
        }
        
        // 文件大小检查
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BusinessException("FILE_SIZE_EXCEED_LIMIT");
        }
        
        // 文件格式检查
        String contentType = file.getContentType();
        if (contentType == null || !Arrays.asList(ALLOWED_TYPES).contains(contentType)) {
            throw new BusinessException("FILE_FORMAT_NOT_SUPPORTED");
        }
        
        // 图片尺寸检查
        try {
            BufferedImage image = ImageIO.read(file.getInputStream());
            if (image == null) {
                throw new BusinessException("FILE_READ_ERROR");
            }
            
            int width = image.getWidth();
            int height = image.getHeight();
            
            if (width < MIN_WIDTH || height < MIN_HEIGHT || width > MAX_WIDTH || height > MAX_HEIGHT) {
                throw new BusinessException("IMAGE_SIZE_NOT_SUPPORTED");
            }
            
        } catch (IOException e) {
            throw new BusinessException("FILE_READ_ERROR");
        }
    }

    /**
     * 生成文件名
     * 
     * @param originalFilename 原始文件名
     * @return 新文件名
     */
    private String generateFileName(String originalFilename) {
        if (originalFilename == null || originalFilename.isEmpty()) {
            originalFilename = "unknown.jpg";
        }
        
        String extension = "";
        int lastDotIndex = originalFilename.lastIndexOf(".");
        if (lastDotIndex > 0) {
            extension = originalFilename.substring(lastDotIndex);
        }
        
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        
        return "book-cover-" + timestamp + "-" + uuid + extension;
    }

    /**
     * 保存文件
     * 
     * @param file 文件
     * @param fileName 文件名
     * @return 文件路径
     */
    private String saveFile(MultipartFile file, String fileName) {
        try {
            // 确保上传目录存在
            File uploadDir = new File(uploadPath + "/images/books");
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }
            
            // 保存文件
            File destFile = new File(uploadDir, fileName);
            file.transferTo(destFile);
            
            return destFile.getAbsolutePath();
            
        } catch (IOException e) {
            log.error("保存文件失败，文件名：{}", fileName, e);
            throw new BusinessException("FILE_SAVE_ERROR");
        }
    }

    /**
     * 格式化日期时间
     * 
     * @param date 日期
     * @return 格式化后的字符串
     */
    private String formatDateTime(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }
}
