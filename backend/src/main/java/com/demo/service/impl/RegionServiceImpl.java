package com.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.demo.exception.BusinessException;
import com.demo.exception.DataNotFoundException;
import com.demo.mapper.RegionMapper;
import com.demo.po.RegionPO;
import com.demo.service.IRegionService;
import com.demo.vo.RegionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 地区信息业务逻辑实现
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
public class RegionServiceImpl implements IRegionService {

    @Autowired
    private RegionMapper regionMapper;

    @Override
    public List<RegionVO> getAllRegions() {
        log.info("查询所有地区列表");
        
        try {
            // 查询地区列表（带图书数量统计）
            List<RegionVO> regionList = regionMapper.selectRegionListWithBookCount();
            
            // 批量处理编码转名称
            processBatchCodeToName(regionList);
            
            log.info("查询地区列表成功，共{}条记录", regionList.size());
            return regionList;
            
        } catch (Exception e) {
            log.error("查询地区列表失败", e);
            throw new BusinessException("SYSTEM_BUSY_ERROR");
        }
    }

    @Override
    public Map<Long, String> batchQueryRegionNames(List<Long> regionIds) {
        if (CollectionUtils.isEmpty(regionIds)) {
            return Collections.emptyMap();
        }
        
        log.info("批量查询地区名称，regionIds: {}", regionIds);
        
        try {
            Map<Long, String> resultMap = regionMapper.batchQueryRegionNames(regionIds);
            log.info("批量查询地区名称成功，返回{}条记录", resultMap.size());
            return resultMap;
            
        } catch (Exception e) {
            log.error("批量查询地区名称失败，regionIds: {}", regionIds, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public RegionVO getRegionById(Long regionId) {
        if (regionId == null) {
            throw new BusinessException("PARAMETER_ERROR");
        }
        
        log.info("根据ID查询地区信息，regionId: {}", regionId);
        
        RegionPO regionPO = regionMapper.selectOne(
            new LambdaQueryWrapper<RegionPO>()
                .eq(RegionPO::getOid, regionId)
                .eq(RegionPO::getEnableFlag, "T")
        );
        
        if (regionPO == null) {
            throw new DataNotFoundException("REGION_NOT_FOUND");
        }
        
        // PO转VO
        RegionVO regionVO = RegionVO.builder()
                .oid(regionPO.getOid())
                .regionName(regionPO.getRegionName())
                .regionAddress(regionPO.getRegionAddress())
                .contactPhone(regionPO.getContactPhone())
                .regionDescription(regionPO.getRegionDescription())
                .sortOrder(regionPO.getSortOrder())
                .regionStatus(regionPO.getRegionStatus())
                .isPreset(regionPO.getIsPreset())
                .createdDate(regionPO.getCreatedDate())
                .build();
        
        // 获取关联图书数量
        Integer bookCount = regionMapper.getBookCountByRegionId(regionId);
        regionVO.setBookCount(bookCount);
        
        // 处理编码转名称
        processSingleCodeToName(regionVO);
        
        log.info("查询地区信息成功，regionId: {}", regionId);
        return regionVO;
    }

    @Override
    public Boolean checkRegionNameExists(String regionName, Long excludeId) {
        if (regionName == null || regionName.trim().isEmpty()) {
            return false;
        }
        
        log.info("检查地区名称是否存在，regionName: {}, excludeId: {}", regionName, excludeId);
        
        try {
            Integer count = regionMapper.checkRegionNameExists(regionName.trim(), excludeId);
            boolean exists = count != null && count > 0;
            log.info("地区名称存在性检查结果：{}", exists);
            return exists;
            
        } catch (Exception e) {
            log.error("检查地区名称是否存在失败，regionName: {}", regionName, e);
            throw new BusinessException("SYSTEM_BUSY_ERROR");
        }
    }

    @Override
    public Integer getBookCountByRegionId(Long regionId) {
        if (regionId == null) {
            return 0;
        }
        
        log.info("获取地区关联的图书数量，regionId: {}", regionId);
        
        try {
            Integer count = regionMapper.getBookCountByRegionId(regionId);
            log.info("地区关联图书数量：{}", count);
            return count != null ? count : 0;
            
        } catch (Exception e) {
            log.error("获取地区关联图书数量失败，regionId: {}", regionId, e);
            return 0;
        }
    }

    /**
     * 批量处理编码转名称
     * 
     * @param regionList 地区列表
     */
    private void processBatchCodeToName(List<RegionVO> regionList) {
        if (CollectionUtils.isEmpty(regionList)) {
            return;
        }
        
        // 批量处理状态名称转换
        regionList.forEach(this::processSingleCodeToName);
    }

    /**
     * 处理单个对象的编码转名称
     * 
     * @param regionVO 地区VO对象
     */
    private void processSingleCodeToName(RegionVO regionVO) {
        if (regionVO == null) {
            return;
        }
        
        // 地区状态名称转换
        if (regionVO.getRegionStatus() != null) {
            regionVO.setRegionStatusName("T".equals(regionVO.getRegionStatus()) ? "启用" : "禁用");
        }
        
        // 预设标识名称转换
        if (regionVO.getIsPreset() != null) {
            regionVO.setIsPresetName("T".equals(regionVO.getIsPreset()) ? "是" : "否");
        }
        
        // 创建人姓名暂时使用占位符
        regionVO.setCreatedByName("系统管理员");
        regionVO.setLastUpdatedByName("系统管理员");
    }
}
