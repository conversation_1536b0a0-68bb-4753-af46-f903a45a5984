package com.demo.service.impl;

import com.demo.bo.CateringBO;
import com.demo.mapper.CateringMapper;
import com.demo.po.CateringPO;
import com.demo.service.CateringService;
import com.demo.vo.CateringVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 餐饮服务实现类
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
@Slf4j
public class CateringServiceImpl implements CateringService {

    @Autowired
    private CateringMapper cateringMapper;

    // @Autowired
    // private HrAuthRemoteService hrAuthRemoteService; // TODO: 实际项目中用于获取用户信息

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCatering(CateringBO cateringBO, String currentUser, String tenantId) {
        log.info("创建餐饮活动，currentUser: {}, tenantId: {}", currentUser, tenantId);
        
        // 参数验证
        validateCateringBO(cateringBO);
        
        // 构建PO对象
        CateringPO cateringPO = buildCateringPOForCreate(cateringBO, currentUser, tenantId);
        
        // 保存到数据库
        cateringMapper.insert(cateringPO);
        
        log.info("餐饮活动创建成功，ID: {}", cateringPO.getId());
        return cateringPO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCatering(CateringBO cateringBO, String currentUser, String tenantId) {
        log.info("更新餐饮活动，ID: {}, currentUser: {}, tenantId: {}", cateringBO.getId(), currentUser, tenantId);
        
        // 参数验证
        if (cateringBO.getId() == null) {
            throw new IllegalArgumentException("餐饮活动ID不能为空");
        }
        validateCateringBO(cateringBO);
        
        // 检查记录是否存在
        CateringPO existingPO = cateringMapper.selectById(cateringBO.getId());
        if (existingPO == null || !"T".equals(existingPO.getEnableFlag()) || !tenantId.equals(existingPO.getTenantId())) {
            log.warn("餐饮活动不存在或已删除，ID: {}", cateringBO.getId());
            return false;
        }
        
        // 构建更新PO对象
        CateringPO updatePO = buildCateringPOForUpdate(cateringBO, currentUser, existingPO);
        
        // 更新数据库
        int result = cateringMapper.updateById(updatePO);
        
        log.info("餐饮活动更新结果: {}", result > 0 ? "成功" : "失败");
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteCatering(Long id, String currentUser, String tenantId) {
        log.info("逻辑删除餐饮活动，ID: {}, currentUser: {}, tenantId: {}", id, currentUser, tenantId);
        
        if (id == null) {
            throw new IllegalArgumentException("餐饮活动ID不能为空");
        }
        
        // 检查记录是否存在
        CateringPO existingPO = cateringMapper.selectById(id);
        if (existingPO == null || !"T".equals(existingPO.getEnableFlag()) || !tenantId.equals(existingPO.getTenantId())) {
            log.warn("餐饮活动不存在或已删除，ID: {}", id);
            return false;
        }
        
        // 逻辑删除
        CateringPO updatePO = new CateringPO();
        updatePO.setId(id);
        updatePO.setEnableFlag("F");
        updatePO.setLastUpdatedBy(currentUser);
        updatePO.setLastUpdatedDate(new Date());
        
        int result = cateringMapper.updateById(updatePO);
        
        log.info("餐饮活动逻辑删除结果: {}", result > 0 ? "成功" : "失败");
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchDeleteCatering(List<Long> ids, String currentUser, String tenantId) {
        log.info("批量逻辑删除餐饮活动，数量: {}, currentUser: {}, tenantId: {}", ids.size(), currentUser, tenantId);
        
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        
        // 使用Mapper的批量删除方法
        Integer result = cateringMapper.batchLogicDelete(ids, currentUser, tenantId);
        
        log.info("餐饮活动批量逻辑删除结果: {} 条", result);
        return result;
    }

    @Override
    public CateringVO getCateringById(Long id, String tenantId, boolean isEnglish) {
        log.info("根据ID查询餐饮活动，ID: {}, tenantId: {}, isEnglish: {}", id, tenantId, isEnglish);
        
        if (id == null) {
            throw new IllegalArgumentException("餐饮活动ID不能为空");
        }
        
        CateringPO cateringPO = cateringMapper.selectById(id);
        if (cateringPO == null || !"T".equals(cateringPO.getEnableFlag()) || !tenantId.equals(cateringPO.getTenantId())) {
            return null;
        }
        
        // 转换为VO并填充扩展信息
        CateringVO cateringVO = convertPOToVO(cateringPO);
        List<CateringVO> voList = Arrays.asList(cateringVO);
        fillExtendedInfo(voList, isEnglish);
        
        return cateringVO;
    }

    @Override
    public List<CateringVO> queryCateringPage(String tenantId, Integer pageNum, Integer pageSize, boolean isEnglish) {
        log.info("分页查询餐饮活动，tenantId: {}, pageNum: {}, pageSize: {}, isEnglish: {}", 
                tenantId, pageNum, pageSize, isEnglish);
        
        // 计算偏移量
        Integer offset = (pageNum - 1) * pageSize;
        
        // 查询数据
        List<CateringPO> cateringPOList = cateringMapper.selectPageByTenant(tenantId, "T", offset, pageSize);
        
        if (CollectionUtils.isEmpty(cateringPOList)) {
            return new ArrayList<>();
        }
        
        // 转换为VO并填充扩展信息
        List<CateringVO> cateringVOList = cateringPOList.stream()
                .map(this::convertPOToVO)
                .collect(Collectors.toList());
        
        fillExtendedInfo(cateringVOList, isEnglish);
        
        return cateringVOList;
    }

    @Override
    public Integer countCatering(String tenantId) {
        log.info("统计餐饮活动总数，tenantId: {}", tenantId);
        
        return cateringMapper.countByTenant(tenantId, "T");
    }

    @Override
    public List<CateringVO> queryCateringByTitle(String title, String tenantId, boolean isEnglish) {
        log.info("根据标题模糊查询餐饮活动，title: {}, tenantId: {}, isEnglish: {}", title, tenantId, isEnglish);
        
        if (StringUtils.isBlank(title)) {
            return new ArrayList<>();
        }
        
        // 查询数据
        List<CateringPO> cateringPOList = cateringMapper.selectByTitleLike(title, tenantId, "T");
        
        if (CollectionUtils.isEmpty(cateringPOList)) {
            return new ArrayList<>();
        }
        
        // 转换为VO并填充扩展信息
        List<CateringVO> cateringVOList = cateringPOList.stream()
                .map(this::convertPOToVO)
                .collect(Collectors.toList());
        
        fillExtendedInfo(cateringVOList, isEnglish);
        
        return cateringVOList;
    }

    /**
     * 验证CateringBO对象
     */
    private void validateCateringBO(CateringBO cateringBO) {
        if (cateringBO == null) {
            throw new IllegalArgumentException("餐饮活动信息不能为空");
        }
        
        if (StringUtils.isBlank(cateringBO.getTitle())) {
            throw new IllegalArgumentException("餐饮活动标题不能为空");
        }
        
        if (cateringBO.getTitle().length() > 128) {
            throw new IllegalArgumentException("餐饮活动标题长度不能超过128个字符");
        }
    }

    /**
     * 构建创建时的PO对象
     */
    private CateringPO buildCateringPOForCreate(CateringBO cateringBO, String currentUser, String tenantId) {
        CateringPO cateringPO = CateringPO.builder()
                .title(cateringBO.getTitle())
                .reason(cateringBO.getReason())
                .occurTime(cateringBO.getOccurTime())
                .location(cateringBO.getLocation())
                .participants(cateringBO.getParticipants())
                .createdBy(currentUser)
                .createdDate(new Date())
                .lastUpdatedBy(currentUser)
                .lastUpdatedDate(new Date())
                .tenantId(tenantId)
                .enableFlag("T")
                .build();
        
        return cateringPO;
    }

    /**
     * 构建更新时的PO对象
     */
    private CateringPO buildCateringPOForUpdate(CateringBO cateringBO, String currentUser, CateringPO existingPO) {
        CateringPO updatePO = new CateringPO();
        updatePO.setId(cateringBO.getId());
        updatePO.setTitle(cateringBO.getTitle());
        updatePO.setReason(cateringBO.getReason());
        updatePO.setOccurTime(cateringBO.getOccurTime());
        updatePO.setLocation(cateringBO.getLocation());
        updatePO.setParticipants(cateringBO.getParticipants());
        updatePO.setLastUpdatedBy(currentUser);
        updatePO.setLastUpdatedDate(new Date());
        
        return updatePO;
    }

    /**
     * 将PO转换为VO
     */
    private CateringVO convertPOToVO(CateringPO cateringPO) {
        return CateringVO.builder()
                .id(cateringPO.getId())
                .title(cateringPO.getTitle())
                .reason(cateringPO.getReason())
                .occurTime(cateringPO.getOccurTime())
                .location(cateringPO.getLocation())
                .participants(cateringPO.getParticipants())
                .build();
    }

    /**
     * 填充扩展信息（编码转名称）
     * 注意：实际项目中需要根据业务规范实现批量查询逻辑
     */
    private void fillExtendedInfo(List<CateringVO> cateringVOList, boolean isEnglish) {
        if (CollectionUtils.isEmpty(cateringVOList)) {
            return;
        }
        
        // 实际项目中的实现示例：
        // 1. 收集所有需要查询的用户编码
        // 2. 批量调用外部接口获取用户名称
        // 3. 批量设置名称到VO对象
        
        // 临时实现：设置默认值
        cateringVOList.forEach(vo -> {
            vo.setCreatedByName("系统管理员");
            vo.setLastUpdatedByName("系统管理员");
        });
        
        log.debug("餐饮活动扩展信息填充完成，数量: {}", cateringVOList.size());
    }
}