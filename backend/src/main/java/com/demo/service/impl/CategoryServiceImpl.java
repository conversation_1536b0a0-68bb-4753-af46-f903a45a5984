package com.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.demo.exception.BusinessException;
import com.demo.exception.DataNotFoundException;
import com.demo.mapper.CategoryMapper;
import com.demo.po.CategoryPO;
import com.demo.service.ICategoryService;
import com.demo.util.CopyUtil;
import com.demo.util.ErrorCode;
import com.demo.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分类信息业务逻辑实现
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
public class CategoryServiceImpl implements ICategoryService {

    @Autowired
    private CategoryMapper categoryMapper;

    @Override
    public List<CategoryVO> getAllCategories() {
        log.info("查询所有分类列表");
        
        try {
            // 查询分类列表
            List<CategoryVO> categoryList = categoryMapper.selectAllActiveCategories();
            
            // 如果返回null，初始化为空列表
            if (categoryList == null) {
                categoryList = new ArrayList<>();
            }
            
            // 批量处理编码转名称
            processCodeToNameBatch(categoryList);
            
            log.info("查询分类列表成功，共{}条记录", categoryList.size());
            return categoryList;
            
        } catch (Exception e) {
            log.error("查询分类列表失败", e);
            throw new BusinessException(ErrorCode.SYSTEM_BUSY_ERROR);
        }
    }

    @Override
    public Map<Long, String> batchQueryCategoryNames(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyMap();
        }
        
        log.info("批量查询分类名称，categoryIds: {}", categoryIds);
        
        try {
            Map<Long, String> resultMap = categoryMapper.batchQueryCategoryNames(categoryIds);
            log.info("批量查询分类名称成功，返回{}条记录", resultMap.size());
            return resultMap;
            
        } catch (Exception e) {
            log.error("批量查询分类名称失败，categoryIds: {}", categoryIds, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public CategoryVO getCategoryById(Long categoryId) {
        if (categoryId == null) {
            throw new BusinessException(ErrorCode.PARAMETER_ERROR);
        }
        
        log.info("根据ID查询分类信息，categoryId: {}", categoryId);
        
        CategoryPO categoryPO = categoryMapper.selectOne(
            new LambdaQueryWrapper<CategoryPO>()
                .eq(CategoryPO::getOid, categoryId)
                .eq(CategoryPO::getEnableFlag, "T")
        );
        
        if (categoryPO == null) {
            throw new BusinessException(ErrorCode.CATEGORY_NOT_FOUND);
        }
        
        // PO转VO
        CategoryVO categoryVO = CategoryVO.builder()
                .oid(categoryPO.getOid())
                .categoryName(categoryPO.getCategoryName())
                .categoryDescription(categoryPO.getCategoryDescription())
                .sortOrder(categoryPO.getSortOrder())
                .categoryStatus(categoryPO.getCategoryStatus())
                .isPreset(categoryPO.getIsPreset())
                .createdDate(categoryPO.getCreatedDate())
                .build();
        
        // 获取关联图书数量
        Integer bookCount = categoryMapper.getBookCountByCategoryId(categoryId);
        categoryVO.setBookCount(bookCount);
        
        // 处理编码转名称
        List<CategoryVO> categoryList = Arrays.asList(categoryVO);
        processCodeToNameBatch(categoryList);
        
        log.info("查询分类信息成功，categoryId: {}", categoryId);
        return categoryVO;
    }

    @Override
    public Boolean checkCategoryNameExists(String categoryName, Long excludeId) {
        if (categoryName == null || categoryName.trim().isEmpty()) {
            return false;
        }
        
        log.info("检查分类名称是否存在，categoryName: {}, excludeId: {}", categoryName, excludeId);
        
        try {
            Boolean exists = categoryMapper.checkCategoryNameExists(categoryName.trim(), excludeId);
            log.info("分类名称存在性检查结果：{}", exists);
            return exists != null ? exists : false;
            
        } catch (Exception e) {
            log.error("检查分类名称是否存在失败，categoryName: {}", categoryName, e);
            throw new BusinessException(ErrorCode.SYSTEM_BUSY_ERROR);
        }
    }

    @Override
    public Integer getBookCountByCategoryId(Long categoryId) {
        if (categoryId == null) {
            return 0;
        }
        
        log.info("获取分类关联的图书数量，categoryId: {}", categoryId);
        
        try {
            Integer count = categoryMapper.getBookCountByCategoryId(categoryId);
            log.info("分类关联图书数量：{}", count);
            return count != null ? count : 0;
            
        } catch (Exception e) {
            log.error("获取分类关联图书数量失败，categoryId: {}", categoryId, e);
            return 0;
        }
    }

    @Override
    @Transactional
    public CategoryVO createCategory(CategoryCreateVO createVO) {
        log.info("新增分类，创建信息：{}", createVO);
        
        try {
            // 1. 检查分类名称是否重复
            if (checkCategoryNameExists(createVO.getCategoryName(), null)) {
                throw new BusinessException(ErrorCode.CATEGORY_NAME_ALREADY_EXISTS);
            }
            
            // 2. 构建PO对象
            CategoryPO categoryPO = CategoryPO.builder()
                    .categoryName(createVO.getCategoryName())
                    .categoryDescription(createVO.getCategoryDescription())
                    .sortOrder(createVO.getSortOrder() != null ? createVO.getSortOrder() : 0)
                    .categoryStatus("T")
                    .isPreset("F")
                    .createdBy("system")
                    .createdDate(new Date())
                    .lastUpdatedBy("system")
                    .lastUpdatedDate(new Date())
                    .tenantId("10001")
                    .enableFlag("T")
                    .build();
            
            // 3. 保存到数据库
            categoryMapper.insert(categoryPO);
            
            // 4. 返回结果
            CategoryVO result = getCategoryById(categoryPO.getOid());
            
            log.info("新增分类成功，分类ID：{}", categoryPO.getOid());
            return result;
            
        } catch (Exception e) {
            log.error("新增分类失败，创建信息：{}", createVO, e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException(ErrorCode.SYSTEM_BUSY_ERROR);
        }
    }

    @Override
    @Transactional
    public CategoryVO updateCategory(CategoryUpdateVO updateVO) {
        log.info("编辑分类，更新信息：{}", updateVO);
        
        try {
            // 1. 验证分类是否存在
            CategoryPO existingCategory = categoryMapper.selectById(updateVO.getOid());
            if (existingCategory == null || !"T".equals(existingCategory.getEnableFlag())) {
                throw new BusinessException(ErrorCode.CATEGORY_NOT_FOUND);
            }
            
            // 2. 检查分类名称是否重复（排除自己）
            if (checkCategoryNameExists(updateVO.getCategoryName(), updateVO.getOid())) {
                throw new BusinessException(ErrorCode.CATEGORY_NAME_ALREADY_EXISTS);
            }
            
            // 3. 预设分类名称不可修改
            if ("T".equals(existingCategory.getIsPreset()) && 
                !existingCategory.getCategoryName().equals(updateVO.getCategoryName())) {
                throw new BusinessException("预设分类名称不可修改");
            }
            
            // 4. 构建更新对象
            CategoryPO updatePO = CategoryPO.builder()
                    .oid(updateVO.getOid())
                    .categoryName(updateVO.getCategoryName())
                    .categoryDescription(updateVO.getCategoryDescription())
                    .sortOrder(updateVO.getSortOrder() != null ? updateVO.getSortOrder() : existingCategory.getSortOrder())
                    .categoryStatus(updateVO.getCategoryStatus() != null ? updateVO.getCategoryStatus() : existingCategory.getCategoryStatus())
                    .lastUpdatedBy("system")
                    .lastUpdatedDate(new Date())
                    .build();
            
            // 5. 更新数据库
            categoryMapper.updateById(updatePO);
            
            // 6. 返回更新后的结果
            CategoryVO result = getCategoryById(updateVO.getOid());
            
            log.info("编辑分类成功，分类ID：{}", updateVO.getOid());
            return result;
            
        } catch (Exception e) {
            log.error("编辑分类失败，更新信息：{}", updateVO, e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException(ErrorCode.SYSTEM_BUSY_ERROR);
        }
    }

    @Override
    @Transactional
    public CategoryDeleteResult deleteCategory(Long categoryId) {
        log.info("删除分类，分类ID：{}", categoryId);
        
        try {
            // 1. 验证分类是否存在
            CategoryPO existingCategory = categoryMapper.selectById(categoryId);
            if (existingCategory == null || !"T".equals(existingCategory.getEnableFlag())) {
                throw new BusinessException(ErrorCode.CATEGORY_NOT_FOUND);
            }
            
            // 2. 预设分类不可删除
            if ("T".equals(existingCategory.getIsPreset())) {
                throw new BusinessException("预设分类不可删除");
            }
            
            // 3. 检查是否有关联图书
            Integer bookCount = getBookCountByCategoryId(categoryId);
            if (bookCount > 0) {
                throw new BusinessException("该分类下还有图书，无法删除");
            }
            
            // 4. 逻辑删除
            CategoryPO updatePO = new CategoryPO();
            updatePO.setOid(categoryId);
            updatePO.setEnableFlag("F");
            updatePO.setLastUpdatedBy("system");
            updatePO.setLastUpdatedDate(new Date());
            
            int affectedRows = categoryMapper.updateById(updatePO);
            
            // 5. 构建返回结果
            CategoryDeleteResult result = CategoryDeleteResult.builder()
                    .oid(categoryId)
                    .categoryName(existingCategory.getCategoryName())
                    .isDeleted(affectedRows > 0)
                    .affectedRows(affectedRows)
                    .deleteTime(new Date().toString())
                    .build();
            
            log.info("删除分类成功，分类ID：{}", categoryId);
            return result;
            
        } catch (Exception e) {
            log.error("删除分类失败，分类ID：{}", categoryId, e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException(ErrorCode.SYSTEM_BUSY_ERROR);
        }
    }

    @Override
    @Transactional
    public CategoryVO toggleCategoryStatus(Long categoryId, String status) {
        log.info("切换分类状态，分类ID：{}，新状态：{}", categoryId, status);
        
        try {
            // 1. 验证分类是否存在
            CategoryPO existingCategory = categoryMapper.selectById(categoryId);
            if (existingCategory == null || !"T".equals(existingCategory.getEnableFlag())) {
                throw new BusinessException(ErrorCode.CATEGORY_NOT_FOUND);
            }
            
            // 2. 更新状态
            CategoryPO updatePO = new CategoryPO();
            updatePO.setOid(categoryId);
            updatePO.setCategoryStatus(status);
            updatePO.setLastUpdatedBy("system");
            updatePO.setLastUpdatedDate(new Date());
            
            categoryMapper.updateById(updatePO);
            
            // 3. 返回更新后的结果
            CategoryVO result = getCategoryById(categoryId);
            
            log.info("切换分类状态成功，分类ID：{}，新状态：{}", categoryId, status);
            return result;
            
        } catch (Exception e) {
            log.error("切换分类状态失败，分类ID：{}，新状态：{}", categoryId, status, e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException(ErrorCode.SYSTEM_BUSY_ERROR);
        }
    }

    /**
     * 批量处理编码转名称
     * 
     * @param categoryList 分类列表
     */
    private void processCodeToNameBatch(List<CategoryVO> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return;
        }
        
        // 批量处理状态名称转换
        categoryList.forEach(this::processSingleCodeToName);
    }

    /**
     * 处理单个对象的编码转名称
     * 
     * @param categoryVO 分类VO对象
     */
    private void processSingleCodeToName(CategoryVO categoryVO) {
        if (categoryVO == null) {
            return;
        }
        
        // 分类状态名称转换
        if (categoryVO.getCategoryStatus() != null) {
            categoryVO.setCategoryStatusName("T".equals(categoryVO.getCategoryStatus()) ? "启用" : "禁用");
        }
        
        // 预设标识名称转换
        if (categoryVO.getIsPreset() != null) {
            categoryVO.setIsPresetName("T".equals(categoryVO.getIsPreset()) ? "是" : "否");
        }
        
        // 创建人姓名暂时使用占位符
        categoryVO.setCreatedByName("系统管理员");
        categoryVO.setLastUpdatedByName("系统管理员");
    }
}
