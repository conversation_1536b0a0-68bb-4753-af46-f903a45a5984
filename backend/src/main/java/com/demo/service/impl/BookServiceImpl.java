package com.demo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demo.exception.BusinessException;
import com.demo.exception.DataNotFoundException;
import com.demo.mapper.BookMapper;
import com.demo.po.BookPO;
import com.demo.service.IBookService;
import com.demo.service.ICategoryService;
import com.demo.service.IRegionService;
import com.demo.util.BookStatusEnum;
import com.demo.util.CopyUtil;
import com.demo.util.ErrorCode;
import com.demo.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 图书信息业务逻辑实现
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
@Transactional
public class BookServiceImpl extends ServiceImpl<BookMapper, BookPO> implements IBookService {

    @Autowired
    private BookMapper bookMapper;

    @Autowired
    private ICategoryService categoryService;

    @Autowired
    private IRegionService regionService;

    private static final String DEFAULT_COVER_IMAGE_URL = "/images/books/default-cover.jpg";

    @Override
    public PageResultVO<BookVO> queryBookList(BookQueryVO queryVO) {
        log.info("分页查询图书列表，查询条件：{}", queryVO);
        
        try {
            // 1. 参数预处理
            preprocessQueryParams(queryVO);
            
            // 2. 查询图书数据
            Page<BookVO> page = new Page<>(queryVO.getPageNum(), queryVO.getPageSize());
            IPage<BookVO> bookPage = bookMapper.selectBookListWithCondition(page, queryVO);
            
            // 3. 批量处理编码转名称
            processBatchCodeToName(bookPage.getRecords());
            
            // 4. 构建分页结果
            PageResultVO<BookVO> result = PageResultVO.build(
                    bookPage.getRecords(),
                    bookPage.getTotal(),
                    bookPage.getCurrent(),
                    bookPage.getSize()
            );
            
            log.info("查询图书列表成功，总记录数：{}，当前页记录数：{}", bookPage.getTotal(), bookPage.getRecords().size());
            return result;
            
        } catch (Exception e) {
            log.error("查询图书列表失败，查询条件：{}", queryVO, e);
            throw new BusinessException(ErrorCode.SYSTEM_BUSY_ERROR);
        }
    }

    @Override
    public BookVO getBookById(Long bookId) {
        if (bookId == null) {
            throw new BusinessException(ErrorCode.PARAMETER_ERROR);
        }
        
        log.info("根据ID获取图书详情，bookId：{}", bookId);
        
        try {
            BookVO bookVO = bookMapper.selectBookDetailById(bookId);
            
            if (bookVO == null) {
                throw new DataNotFoundException(ErrorCode.BOOK_NOT_FOUND);
            }
            
            // 处理编码转名称
            processSingleCodeToName(bookVO);
            
            log.info("获取图书详情成功，bookId：{}", bookId);
            return bookVO;
            
        } catch (Exception e) {
            log.error("获取图书详情失败，bookId：{}", bookId, e);
            if (e instanceof BusinessException || e instanceof DataNotFoundException) {
                throw e;
            }
            throw new BusinessException("SYSTEM_BUSY_ERROR");
        }
    }

    @Override
    @Transactional
    public BookVO createBook(BookCreateVO createVO) {
        log.info("新增图书，创建信息：{}", createVO);
        
        try {
            // 1. 业务规则校验
            validateBookCreateRules(createVO);
            
            // 2. 构建PO对象
            BookPO bookPO = buildBookPO(createVO);
            
            // 3. 保存到数据库
            bookMapper.insert(bookPO);
            
            // 4. 返回结果
            BookVO resultVO = BookVO.builder()
                    .oid(bookPO.getOid())
                    .bookTitle(bookPO.getBookTitle())
                    .isbn(bookPO.getIsbn())
                    .build();
            
            log.info("新增图书成功，图书ID：{}", bookPO.getOid());
            return resultVO;
            
        } catch (Exception e) {
            log.error("新增图书失败，创建信息：{}", createVO, e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException(ErrorCode.SYSTEM_BUSY_ERROR);
        }
    }

    @Override
    @Transactional
    public BookVO updateBook(BookUpdateVO updateVO) {
        log.info("编辑图书，更新信息：{}", updateVO);
        
        try {
            // 1. 验证图书是否存在
            BookPO existingBook = bookMapper.selectById(updateVO.getOid());
            if (existingBook == null || !"T".equals(existingBook.getEnableFlag())) {
                throw new BusinessException(ErrorCode.BOOK_NOT_FOUND);
            }
            
            // 2. 业务规则校验
            validateBookUpdateRules(updateVO);
            
            // 3. 构建更新对象
            BookPO updatePO = buildUpdateBookPO(updateVO, existingBook);
            
            // 4. 更新数据库
            bookMapper.updateById(updatePO);
            
            // 5. 返回更新后的结果
            BookVO resultVO = getBookById(updateVO.getOid());
            
            log.info("编辑图书成功，图书ID：{}", updateVO.getOid());
            return resultVO;
            
        } catch (Exception e) {
            log.error("编辑图书失败，更新信息：{}", updateVO, e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException(ErrorCode.SYSTEM_BUSY_ERROR);
        }
    }

    @Override
    @Transactional
    public BookDeleteResult deleteBook(Long bookId) {
        log.info("删除图书，图书ID：{}", bookId);
        
        try {
            // 1. 验证图书是否存在
            BookPO existingBook = this.getById(bookId);
            if (existingBook == null || !"T".equals(existingBook.getEnableFlag())) {
                throw new BusinessException(ErrorCode.BOOK_NOT_FOUND);
            }
            
            // 2. 检查是否有借阅记录（这里简化处理，实际应该检查借阅表）
            if (existingBook.getBorrowedQuantity() != null && existingBook.getBorrowedQuantity() > 0) {
                throw new BusinessException(ErrorCode.BOOK_HAS_BORROW_RECORDS);
            }
            
            // 3. 逻辑删除
            BookPO updatePO = new BookPO();
            updatePO.setOid(bookId);
            updatePO.setEnableFlag("F");
            updatePO.setLastUpdatedBy("system");
            updatePO.setLastUpdatedDate(new Date());
            
            int affectedRows = bookMapper.updateById(updatePO);
            
            // 4. 构建返回结果
            BookDeleteResult result = BookDeleteResult.builder()
                    .oid(bookId)
                    .bookTitle(existingBook.getBookTitle())
                    .isDeleted(affectedRows > 0)
                    .affectedRows(affectedRows)
                    .deleteTime(new Date().toString())
                    .build();
            
            log.info("删除图书成功，图书ID：{}", bookId);
            return result;
            
        } catch (Exception e) {
            log.error("删除图书失败，图书ID：{}", bookId, e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException(ErrorCode.SYSTEM_BUSY_ERROR);
        }
    }

    @Override
    @Transactional
    public BookBatchDeleteResult batchDeleteBooks(List<Long> bookIds) {
        log.info("批量删除图书，图书ID列表：{}", bookIds);
        
        if (CollectionUtils.isEmpty(bookIds)) {
            return BookBatchDeleteResult.builder()
                    .successCount(0)
                    .failureCount(0)
                    .successIds(Collections.emptyList())
                    .failureDetails(Collections.emptyList())
                    .build();
        }
        
        try {
            List<Long> successIds = new ArrayList<>();
            List<BookBatchDeleteResult.FailureDetail> failureDetails = new ArrayList<>();
            
            for (Long bookId : bookIds) {
                try {
                    deleteBook(bookId);
                    successIds.add(bookId);
                } catch (BusinessException e) {
                    failureDetails.add(BookBatchDeleteResult.FailureDetail.builder()
                            .bookId(bookId)
                            .reason(e.getMessage())
                            .errorCode(e.getMessage())
                            .build());
                }
            }
            
            BookBatchDeleteResult result = BookBatchDeleteResult.builder()
                    .successCount(successIds.size())
                    .failureCount(failureDetails.size())
                    .successIds(successIds)
                    .failureDetails(failureDetails)
                    .build();
            
            log.info("批量删除图书完成，成功：{}，失败：{}", successIds.size(), failureDetails.size());
            return result;
            
        } catch (Exception e) {
            log.error("批量删除图书失败，图书ID列表：{}", bookIds, e);
            throw new BusinessException(ErrorCode.SYSTEM_BUSY_ERROR);
        }
    }

    @Override
    public Boolean checkIsbnExists(String isbn, Long excludeId) {
        if (!StringUtils.hasText(isbn)) {
            return false;
        }
        
        log.info("检查ISBN是否存在，isbn：{}，excludeId：{}", isbn, excludeId);
        
        try {
            Integer count = bookMapper.checkIsbnExists(isbn.trim(), excludeId);
            boolean exists = count != null && count > 0;
            log.info("ISBN存在性检查结果：{}", exists);
            return exists;
            
        } catch (Exception e) {
            log.error("检查ISBN是否存在失败，isbn：{}", isbn, e);
            throw new BusinessException(ErrorCode.SYSTEM_BUSY_ERROR);
        }
    }

    /**
     * 参数预处理
     * 
     * @param queryVO 查询条件
     */
    private void preprocessQueryParams(BookQueryVO queryVO) {
        if (queryVO == null) {
            return;
        }
        
        // 搜索关键词去空格
        if (StringUtils.hasText(queryVO.getSearchKeyword())) {
            queryVO.setSearchKeyword(queryVO.getSearchKeyword().trim());
        }
        
        // 分页参数默认值
        if (queryVO.getPageNum() == null || queryVO.getPageNum() < 1) {
            queryVO.setPageNum(1);
        }
        if (queryVO.getPageSize() == null || queryVO.getPageSize() < 1) {
            queryVO.setPageSize(10);
        }
        if (queryVO.getPageSize() > 100) {
            queryVO.setPageSize(100);
        }
    }

    /**
     * 批量处理编码转名称
     * 
     * @param bookList 图书列表
     */
    private void processBatchCodeToName(List<BookVO> bookList) {
        if (CollectionUtils.isEmpty(bookList)) {
            return;
        }
        
        // 批量获取分类名称
        List<Long> categoryIds = bookList.stream()
                .map(BookVO::getCategoryId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> categoryNameMap = categoryService.batchQueryCategoryNames(categoryIds);
        
        // 批量获取地区名称
        List<Long> regionIds = bookList.stream()
                .map(BookVO::getRegionId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> regionNameMap = regionService.batchQueryRegionNames(regionIds);
        
        // 批量设置名称
        bookList.forEach(bookVO -> {
            bookVO.setCategoryName(categoryNameMap.get(bookVO.getCategoryId()));
            bookVO.setRegionName(regionNameMap.get(bookVO.getRegionId()));
            bookVO.setBookStatusName(BookStatusEnum.getDesc(bookVO.getBookStatus(), false));
            
            // 创建人姓名暂时使用占位符
            bookVO.setCreatedByName("系统管理员");
            bookVO.setLastUpdatedByName("系统管理员");
        });
    }

    /**
     * 处理单个对象的编码转名称
     * 
     * @param bookVO 图书VO对象
     */
    private void processSingleCodeToName(BookVO bookVO) {
        if (bookVO == null) {
            return;
        }
        
        // 分类名称
        if (bookVO.getCategoryId() != null) {
            Map<Long, String> categoryNameMap = categoryService.batchQueryCategoryNames(
                    Collections.singletonList(bookVO.getCategoryId()));
            bookVO.setCategoryName(categoryNameMap.get(bookVO.getCategoryId()));
        }
        
        // 地区名称
        if (bookVO.getRegionId() != null) {
            Map<Long, String> regionNameMap = regionService.batchQueryRegionNames(
                    Collections.singletonList(bookVO.getRegionId()));
            bookVO.setRegionName(regionNameMap.get(bookVO.getRegionId()));
        }
        
        // 图书状态名称
        bookVO.setBookStatusName(BookStatusEnum.getDesc(bookVO.getBookStatus(), false));
        
        // 创建人姓名暂时使用占位符
        bookVO.setCreatedByName("系统管理员");
        bookVO.setLastUpdatedByName("系统管理员");
    }

    /**
     * 验证图书创建业务规则
     * 
     * @param createVO 创建信息
     */
    private void validateBookCreateRules(BookCreateVO createVO) {
        // ISBN唯一性检查
        if (checkIsbnExists(createVO.getIsbn(), null)) {
            throw new BusinessException(ErrorCode.ISBN_ALREADY_EXISTS);
        }
        
        // 分类有效性检查
        try {
            CategoryVO category = categoryService.getCategoryById(createVO.getCategoryId());
            if (category == null) {
                throw new BusinessException(ErrorCode.CATEGORY_NOT_FOUND);
            }
        } catch (DataNotFoundException e) {
            throw new BusinessException(ErrorCode.CATEGORY_NOT_FOUND);
        }
        
        // 地区有效性检查
        try {
            RegionVO region = regionService.getRegionById(createVO.getRegionId());
            if (region == null) {
                throw new BusinessException(ErrorCode.REGION_NOT_FOUND);
            }
        } catch (DataNotFoundException e) {
            throw new BusinessException(ErrorCode.REGION_NOT_FOUND);
        }
    }

    /**
     * 构建图书PO对象
     * 
     * @param createVO 创建信息
     * @return 图书PO对象
     */
    private BookPO buildBookPO(BookCreateVO createVO) {
        Integer totalQuantity = createVO.getTotalQuantity() != null ? createVO.getTotalQuantity() : 1;
        
        return BookPO.builder()
                .bookTitle(createVO.getBookTitle())
                .bookAuthor(createVO.getBookAuthor())
                .isbn(createVO.getIsbn())
                .categoryId(createVO.getCategoryId())
                .regionId(createVO.getRegionId())
                .totalQuantity(totalQuantity)
                .availableQuantity(totalQuantity)
                .borrowedQuantity(0)
                .publicationYear(createVO.getPublicationYear())
                .bookDescription(createVO.getBookDescription())
                .coverImageUrl(StringUtils.hasText(createVO.getCoverImageUrl()) 
                    ? createVO.getCoverImageUrl() : DEFAULT_COVER_IMAGE_URL)
                .bookStatus("AVAILABLE")
                .createdBy("system") // 暂时使用system，实际应该从用户上下文获取
                .createdDate(new Date())
                .lastUpdatedBy("system")
                .lastUpdatedDate(new Date())
                .tenantId("10001")
                .enableFlag("T")
                .build();
    }

    /**
     * 验证图书编辑业务规则
     * 
     * @param updateVO 编辑信息
     */
    private void validateBookUpdateRules(BookUpdateVO updateVO) {
        // ISBN唯一性检查（排除自己）
        if (checkIsbnExists(updateVO.getIsbn(), updateVO.getOid())) {
            throw new BusinessException(ErrorCode.ISBN_ALREADY_EXISTS);
        }
        
        // 分类有效性检查
        try {
            CategoryVO category = categoryService.getCategoryById(updateVO.getCategoryId());
            if (category == null) {
                throw new BusinessException(ErrorCode.CATEGORY_NOT_FOUND);
            }
        } catch (DataNotFoundException e) {
            throw new BusinessException(ErrorCode.CATEGORY_NOT_FOUND);
        }
        
        // 地区有效性检查
        try {
            RegionVO region = regionService.getRegionById(updateVO.getRegionId());
            if (region == null) {
                throw new BusinessException(ErrorCode.REGION_NOT_FOUND);
            }
        } catch (DataNotFoundException e) {
            throw new BusinessException(ErrorCode.REGION_NOT_FOUND);
        }
    }

    /**
     * 构建图书更新PO对象
     * 
     * @param updateVO 编辑信息
     * @param existingBook 现有图书信息
     * @return 图书PO对象
     */
    private BookPO buildUpdateBookPO(BookUpdateVO updateVO, BookPO existingBook) {
        // 计算可借册数：如果总册数发生变化，需要重新计算可借册数
        int borrowedQuantity = existingBook.getBorrowedQuantity() != null ? existingBook.getBorrowedQuantity() : 0;
        int newAvailableQuantity = Math.max(0, updateVO.getTotalQuantity() - borrowedQuantity);
        
        return BookPO.builder()
                .oid(updateVO.getOid())
                .bookTitle(updateVO.getBookTitle())
                .bookAuthor(updateVO.getBookAuthor())
                .isbn(updateVO.getIsbn())
                .categoryId(updateVO.getCategoryId())
                .regionId(updateVO.getRegionId())
                .totalQuantity(updateVO.getTotalQuantity())
                .availableQuantity(newAvailableQuantity)
                .borrowedQuantity(borrowedQuantity) // 保持不变
                .publicationYear(updateVO.getPublicationYear())
                .bookDescription(updateVO.getBookDescription())
                .coverImageUrl(StringUtils.hasText(updateVO.getCoverImageUrl()) 
                    ? updateVO.getCoverImageUrl() : existingBook.getCoverImageUrl())
                .bookStatus(existingBook.getBookStatus()) // 保持现有状态
                .lastUpdatedBy("system") // 暂时使用system，实际应该从用户上下文获取
                .lastUpdatedDate(new Date())
                .build();
    }
}
