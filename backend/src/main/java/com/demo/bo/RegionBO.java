package com.demo.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 地区信息业务对象
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                                    // ✅ 必须：Lombok数据注解
@Builder                                // ✅ 推荐：构建者模式
@AllArgsConstructor                     // ✅ 推荐：全参构造函数
@NoArgsConstructor                      // ✅ 必须：无参构造函数
public class RegionBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 地区ID
     */
    private Long oid;

    /**
     * 地区名称
     */
    private String regionName;

    /**
     * 地区地址
     */
    private String regionAddress;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 地区描述
     */
    private String regionDescription;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 地区状态
     */
    private String regionStatus;

    /**
     * 是否预设地区
     */
    private String isPreset;

    /**
     * 关联图书数量
     */
    private Integer bookCount;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 地区状态名称
     */
    private String regionStatusName;

    /**
     * 预设标识名称
     */
    private String isPresetName;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 更新人姓名
     */
    private String lastUpdatedByName;

    // ===== 业务方法 =====

    /**
     * 判断是否为预设地区
     * @return true：预设地区，false：非预设地区
     */
    public Boolean isPresetRegion() {
        return "T".equals(this.isPreset);
    }

    /**
     * 判断地区是否启用
     * @return true：启用，false：禁用
     */
    public Boolean isEnabled() {
        return "T".equals(this.regionStatus);
    }

    /**
     * 判断是否可以删除
     * @return true：可删除，false：不可删除
     */
    public Boolean isDeletable() {
        // 预设地区不可删除，有关联图书的地区不可删除
        return !isPresetRegion() && (bookCount == null || bookCount == 0);
    }

    /**
     * 判断是否可以修改名称
     * @return true：可修改，false：不可修改
     */
    public Boolean isNameEditable() {
        // 预设地区名称不可修改
        return !isPresetRegion();
    }

    /**
     * 获取完整地址信息
     * @return 地区名称 + 地区地址
     */
    public String getFullAddress() {
        if (regionName != null && regionAddress != null) {
            return regionName + " - " + regionAddress;
        } else if (regionName != null) {
            return regionName;
        } else if (regionAddress != null) {
            return regionAddress;
        }
        return "";
    }
}
