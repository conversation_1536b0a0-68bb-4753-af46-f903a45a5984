package com.demo.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 餐饮业务对象
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                                    // ✅ 必须：Lombok数据注解，自动生成getter/setter/toString/equals/hashCode
@Builder                                // ✅ 必须：构建者模式
@NoArgsConstructor                      // ✅ 必须：无参构造函数
@AllArgsConstructor                     // ✅ 必须：全参构造函数
public class CateringBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "餐饮活动标题", required = true)
    private String title;

    @ApiModelProperty(value = "活动原因说明")
    private String reason;

    @ApiModelProperty(value = "活动发生时间")
    private Date occurTime;

    @ApiModelProperty(value = "活动地点")
    private String location;

    @ApiModelProperty(value = "参与人员")
    private String participants;

    /**
     * 创建Builder实例的静态方法（推荐）
     */
    public static CateringBOBuilder builder() {
        return new CateringBOBuilder();
    }

    /**
     * 支持链式调用的构建方法（推荐）
     */
    public static class CateringBOBuilder {
        
        /**
         * 构建并验证对象
         */
        public CateringBO buildAndValidate() {
            CateringBO bo = this.build();
            validateRequired(bo);
            return bo;
        }
        
        /**
         * 验证必填字段
         */
        private void validateRequired(CateringBO bo) {
            if (bo.getTitle() == null || bo.getTitle().trim().isEmpty()) {
                throw new IllegalArgumentException("餐饮活动标题不能为空");
            }
            // 可以根据业务需求添加更多验证逻辑
        }
    }
}