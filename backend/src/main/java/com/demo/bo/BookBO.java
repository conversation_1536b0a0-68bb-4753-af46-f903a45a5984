package com.demo.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 图书信息业务对象
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                                    // ✅ 必须：Lombok数据注解
@Builder                                // ✅ 推荐：构建者模式
@AllArgsConstructor                     // ✅ 推荐：全参构造函数
@NoArgsConstructor                      // ✅ 必须：无参构造函数
public class BookBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 图书ID
     */
    private Long oid;

    /**
     * 图书标题
     */
    private String bookTitle;

    /**
     * 图书作者
     */
    private String bookAuthor;

    /**
     * ISBN编号
     */
    private String isbn;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 地区ID
     */
    private Long regionId;

    /**
     * 总册数
     */
    private Integer totalQuantity;

    /**
     * 可借册数
     */
    private Integer availableQuantity;

    /**
     * 已借册数
     */
    private Integer borrowedQuantity;

    /**
     * 出版年份
     */
    private Integer publicationYear;

    /**
     * 图书描述
     */
    private String bookDescription;

    /**
     * 封面图片URL
     */
    private String coverImageUrl;

    /**
     * 图书状态
     */
    private String bookStatus;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 地区名称
     */
    private String regionName;

    /**
     * 图书状态名称
     */
    private String bookStatusName;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 更新人姓名
     */
    private String lastUpdatedByName;

    // ===== 业务方法 =====

    /**
     * 计算借阅率
     * @return 借阅率（0-1之间的小数）
     */
    public Double getBorrowRate() {
        if (totalQuantity == null || totalQuantity == 0) {
            return 0.0;
        }
        if (borrowedQuantity == null) {
            return 0.0;
        }
        return (double) borrowedQuantity / totalQuantity;
    }

    /**
     * 判断图书是否可借阅
     * @return true：可借阅，false：不可借阅
     */
    public Boolean isAvailable() {
        return "AVAILABLE".equals(bookStatus) && availableQuantity != null && availableQuantity > 0;
    }

    /**
     * 判断图书是否可以删除
     * @return true：可删除，false：不可删除
     */
    public Boolean isDeletable() {
        // 有借出记录的图书不允许删除
        return borrowedQuantity == null || borrowedQuantity == 0;
    }

    /**
     * 计算可借阅数量
     * @return 可借阅数量
     */
    public Integer calculateAvailableQuantity() {
        if (totalQuantity == null) {
            return 0;
        }
        if (borrowedQuantity == null) {
            return totalQuantity;
        }
        return Math.max(0, totalQuantity - borrowedQuantity);
    }

    /**
     * 判断是否为新图书（近6个月内创建）
     * @return true：新图书，false：老图书
     */
    public Boolean isNewBook() {
        if (createdDate == null) {
            return false;
        }
        long sixMonthsAgo = System.currentTimeMillis() - (6L * 30 * 24 * 60 * 60 * 1000);
        return createdDate.getTime() > sixMonthsAgo;
    }

    /**
     * 判断是否为热门图书（借阅率>50%）
     * @return true：热门图书，false：普通图书
     */
    public Boolean isPopularBook() {
        return getBorrowRate() > 0.5;
    }

    /**
     * 获取图书状态描述
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (!isAvailable()) {
            return "不可借阅";
        }
        if (isPopularBook()) {
            return "热门图书";
        }
        if (isNewBook()) {
            return "新书推荐";
        }
        return "可借阅";
    }
}
