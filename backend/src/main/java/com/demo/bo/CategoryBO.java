package com.demo.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 分类信息业务对象
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                                    // ✅ 必须：Lombok数据注解
@Builder                                // ✅ 推荐：构建者模式
@AllArgsConstructor                     // ✅ 推荐：全参构造函数
@NoArgsConstructor                      // ✅ 必须：无参构造函数
public class CategoryBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分类ID
     */
    private Long oid;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 分类描述
     */
    private String categoryDescription;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 分类状态
     */
    private String categoryStatus;

    /**
     * 是否预设分类
     */
    private String isPreset;

    /**
     * 关联图书数量
     */
    private Integer bookCount;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 分类状态名称
     */
    private String categoryStatusName;

    /**
     * 预设标识名称
     */
    private String isPresetName;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 更新人姓名
     */
    private String lastUpdatedByName;

    // ===== 业务方法 =====

    /**
     * 判断是否为预设分类
     * @return true：预设分类，false：非预设分类
     */
    public Boolean isPresetCategory() {
        return "T".equals(this.isPreset);
    }

    /**
     * 判断分类是否启用
     * @return true：启用，false：禁用
     */
    public Boolean isEnabled() {
        return "T".equals(this.categoryStatus);
    }

    /**
     * 判断是否可以删除
     * @return true：可删除，false：不可删除
     */
    public Boolean isDeletable() {
        // 预设分类不可删除，有关联图书的分类不可删除
        return !isPresetCategory() && (bookCount == null || bookCount == 0);
    }

    /**
     * 判断是否可以修改名称
     * @return true：可修改，false：不可修改
     */
    public Boolean isNameEditable() {
        // 预设分类名称不可修改
        return !isPresetCategory();
    }
}
