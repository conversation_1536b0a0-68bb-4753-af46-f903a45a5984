package com.demo.factory;



import com.demo.bo.CateringBO;
import com.demo.po.CateringPO;
import com.demo.vo.CateringVO;

import org.springframework.stereotype.Component;
import org.springframework.beans.BeanUtils;

/* Started by AICoder */
// factory类
@Component
public class CateringFactory {

    public static CateringPO convertBOToPO(CateringBO cateringBO) {
        CateringPO cateringPO = new CateringPO();
        BeanUtils.copyProperties(cateringBO, cateringPO);
        return cateringPO;
    }

    public static CateringVO convertPOToVO(CateringPO cateringPO) {
        CateringVO cateringVO = new CateringVO();
        BeanUtils.copyProperties(cateringPO, cateringVO);
        return cateringVO;
    }
}
/* Ended by AICoder */