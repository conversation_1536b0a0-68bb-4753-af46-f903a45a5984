package com.demo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 图书批量删除结果VO
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "图书批量删除结果VO")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BookBatchDeleteResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "成功删除数量", required = true, example = "5")
    private Integer successCount;

    @ApiModelProperty(value = "删除失败数量", required = true, example = "2")
    private Integer failureCount;

    @ApiModelProperty(value = "成功删除的图书ID列表", required = true, example = "[1, 2, 3, 4, 5]")
    private List<Long> successIds;

    @ApiModelProperty(value = "失败详情列表", required = false)
    private List<FailureDetail> failureDetails;

    /**
     * 失败详情
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FailureDetail implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "图书ID", required = true, example = "6")
        private Long bookId;

        @ApiModelProperty(value = "失败原因", required = true, example = "该图书还有借阅记录，无法删除")
        private String reason;

        @ApiModelProperty(value = "错误码", required = false, example = "B003")
        private String errorCode;
    }
}
