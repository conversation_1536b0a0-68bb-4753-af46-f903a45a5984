package com.demo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 分类信息视图对象
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                                    // ✅ 必须：Lombok数据注解
@ApiModel(value = "分类信息VO")          // ✅ 必须：Swagger模型注解
@Builder                                // ✅ 推荐：构建者模式
@AllArgsConstructor                     // ✅ 推荐：全参构造函数
@NoArgsConstructor                      // ✅ 必须：无参构造函数
public class CategoryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分类ID", required = false, example = "1")
    private Long oid;

    @ApiModelProperty(value = "分类名称", required = true, example = "文学艺术")
    @NotBlank(message = "{validation.category.name.not.blank}")
    @Size(max = 50, message = "{validation.category.name.size.max}")
    private String categoryName;

    @ApiModelProperty(value = "分类描述", required = false, example = "文学、艺术、美学类图书")
    @Size(max = 500, message = "{validation.category.description.size.max}")
    private String categoryDescription;

    @ApiModelProperty(value = "排序号", required = false, example = "1")
    private Integer sortOrder;

    @ApiModelProperty(value = "分类状态", required = false, example = "T")
    private String categoryStatus;

    @ApiModelProperty(value = "是否预设分类", required = false, example = "T")
    private String isPreset;

    @ApiModelProperty(value = "关联图书数量", required = false, example = "10")
    private Integer bookCount;

    @ApiModelProperty(value = "创建人", required = false, example = "admin")
    private String createdBy;

    @ApiModelProperty(value = "创建时间", required = false, example = "2024-01-15 10:30:00")
    private Date createdDate;

    @ApiModelProperty(value = "更新人", required = false, example = "admin")
    private String lastUpdatedBy;

    @ApiModelProperty(value = "更新时间", required = false, example = "2024-01-15 10:30:00")
    private Date lastUpdatedDate;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = false, example = "10001")
    private String tenantId;

    /**
     * 有效标识 T：有效，F：无效
     */
    @ApiModelProperty(value = "有效标识 T：有效，F：无效", required = false, example = "T")
    private String enableFlag;

    // ===== 编码转名称字段 =====
    
    @ApiModelProperty(value = "分类状态名称", required = false, example = "启用")
    private String categoryStatusName;

    @ApiModelProperty(value = "预设标识名称", required = false, example = "是")
    private String isPresetName;

    @ApiModelProperty(value = "创建人姓名", required = false, example = "系统管理员")
    private String createdByName;

    @ApiModelProperty(value = "更新人姓名", required = false, example = "系统管理员")
    private String lastUpdatedByName;

    /**
     * 获取分类状态名称（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getCategoryStatusNameFromCode() {
        return this.categoryStatusName;
    }

    /**
     * 获取预设标识名称（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getIsPresetNameFromCode() {
        return this.isPresetName;
    }

    /**
     * 获取创建人姓名（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getCreatedByNameFromCode() {
        return this.createdByName;
    }

    /**
     * 获取更新人姓名（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getLastUpdatedByNameFromCode() {
        return this.lastUpdatedByName;
    }
}
