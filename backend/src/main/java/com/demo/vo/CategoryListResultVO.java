package com.demo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 分类列表查询结果VO
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Builder
@ApiModel("分类列表查询结果")
public class CategoryListResultVO {

    @ApiModelProperty("分类列表")
    private List<CategoryVO> categories;

    @ApiModelProperty("分类总数")
    private Integer total;

    /**
     * 静态构建方法
     * 
     * @param categories 分类列表
     * @return 分类列表结果VO
     */
    public static CategoryListResultVO of(List<CategoryVO> categories) {
        return CategoryListResultVO.builder()
                .categories(categories)
                .total(categories != null ? categories.size() : 0)
                .build();
    }
}
