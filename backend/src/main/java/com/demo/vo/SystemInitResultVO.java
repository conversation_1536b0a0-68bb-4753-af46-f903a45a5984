package com.demo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 系统初始化结果视图对象
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                                    // ✅ 必须：Lombok数据注解
@ApiModel(value = "系统初始化结果VO")    // ✅ 必须：Swagger模型注解
@Builder                                // ✅ 推荐：构建者模式
@AllArgsConstructor                     // ✅ 推荐：全参构造函数
@NoArgsConstructor                      // ✅ 必须：无参构造函数
public class SystemInitResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否初始化成功", required = true, example = "true")
    private Boolean isInitialized;

    @ApiModelProperty(value = "创建的分类数量", required = true, example = "8")
    private Integer categoryCount;

    @ApiModelProperty(value = "创建的地区数量", required = true, example = "3")
    private Integer regionCount;

    @ApiModelProperty(value = "错误信息", required = false, example = "null")
    private String errorMessage;
}
