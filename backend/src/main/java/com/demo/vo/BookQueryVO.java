package com.demo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 图书查询条件视图对象
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                                    // ✅ 必须：Lombok数据注解
@ApiModel(value = "图书查询条件VO")      // ✅ 必须：Swagger模型注解
@Builder                                // ✅ 推荐：构建者模式
@AllArgsConstructor                     // ✅ 推荐：全参构造函数
@NoArgsConstructor                      // ✅ 必须：无参构造函数
public class BookQueryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "搜索关键词（书名、作者模糊查询）", required = false, example = "Java编程")
    @Size(max = 50, message = "{validation.search.keyword.size.max}")
    private String searchKeyword;

    @ApiModelProperty(value = "图书作者（用于兼容前端author字段）", required = false, example = "Bruce Eckel")
    @Size(max = 300, message = "{validation.book.author.size.max}")
    @com.fasterxml.jackson.annotation.JsonProperty("author")
    private String bookAuthor;

    @ApiModelProperty(value = "图书标题（精确查询）", required = false, example = "Java编程思想")
    @Size(max = 100, message = "{validation.book.title.size.max}")
    private String bookTitle;

    @ApiModelProperty(value = "分类ID（精确查询）", required = false, example = "1")
    @Min(value = 1, message = "{validation.category.id.min}")
    private Long categoryId;

    @ApiModelProperty(value = "地区ID（精确查询）", required = false, example = "1")
    @Min(value = 1, message = "{validation.region.id.min}")
    private Long regionId;

    @ApiModelProperty(value = "图书状态（精确查询）", required = false, example = "AVAILABLE")
    private String bookStatus;

    @ApiModelProperty(value = "出版年份开始", required = false, example = "2000")
    @Min(value = 1900, message = "{validation.book.year.min}")
    private Integer publicationYearStart;

    @ApiModelProperty(value = "出版年份结束", required = false, example = "2024")
    @Max(value = 2100, message = "{validation.book.year.max}")
    private Integer publicationYearEnd;

    @ApiModelProperty(value = "页码", required = false, example = "1")
    @Min(value = 1, message = "{validation.page.num.min}")
    @Builder.Default
    private Integer pageNum = 1;

    @ApiModelProperty(value = "页大小", required = false, example = "10")
    @Min(value = 1, message = "{validation.page.size.min}")
    @Max(value = 100, message = "{validation.page.size.max}")
    @Builder.Default
    private Integer pageSize = 10;
}
