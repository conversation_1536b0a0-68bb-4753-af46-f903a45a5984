package com.demo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 统一API响应格式
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiResponseVO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应码信息
     */
    private CodeInfo code;

    /**
     * 业务数据
     */
    private T bo;

    /**
     * 其他信息（如错误详情）
     */
    private Object other;

    /**
     * 响应码信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CodeInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 响应码：0000表示成功
         */
        private String code;

        /**
         * 消息ID
         */
        private String msgId;
    }

    /**
     * 成功响应（无数据）
     */
    public static <T> ApiResponseVO<T> success() {
        return ApiResponseVO.<T>builder()
                .code(CodeInfo.builder()
                        .code("0000")
                        .msgId("SUCCESS")
                        .build())
                .bo(null)
                .other(null)
                .build();
    }

    /**
     * 成功响应（带数据）
     */
    public static <T> ApiResponseVO<T> success(T data) {
        return ApiResponseVO.<T>builder()
                .code(CodeInfo.builder()
                        .code("0000")
                        .msgId("SUCCESS")
                        .build())
                .bo(data)
                .other(null)
                .build();
    }

    /**
     * 成功响应（带数据和消息）
     */
    public static <T> ApiResponseVO<T> success(T data, String msgId) {
        return ApiResponseVO.<T>builder()
                .code(CodeInfo.builder()
                        .code("0000")
                        .msgId(msgId != null ? msgId : "SUCCESS")
                        .build())
                .bo(data)
                .other(null)
                .build();
    }

    /**
     * 业务错误响应
     */
    public static <T> ApiResponseVO<T> businessError(String errorCode, String msgId) {
        return ApiResponseVO.<T>builder()
                .code(CodeInfo.builder()
                        .code(errorCode)
                        .msgId(msgId)
                        .build())
                .bo(null)
                .other(null)
                .build();
    }

    /**
     * 业务错误响应（带错误详情）
     */
    public static <T> ApiResponseVO<T> businessError(String errorCode, String msgId, Object errorDetails) {
        return ApiResponseVO.<T>builder()
                .code(CodeInfo.builder()
                        .code(errorCode)
                        .msgId(msgId)
                        .build())
                .bo(null)
                .other(errorDetails)
                .build();
    }

    /**
     * 系统错误响应
     */
    public static <T> ApiResponseVO<T> systemError() {
        return ApiResponseVO.<T>builder()
                .code(CodeInfo.builder()
                        .code("9999")
                        .msgId("SYSTEM_ERROR")
                        .build())
                .bo(null)
                .other(null)
                .build();
    }

    /**
     * 系统错误响应（带消息）
     */
    public static <T> ApiResponseVO<T> systemError(String msgId) {
        return ApiResponseVO.<T>builder()
                .code(CodeInfo.builder()
                        .code("9999")
                        .msgId(msgId != null ? msgId : "SYSTEM_ERROR")
                        .build())
                .bo(null)
                .other(null)
                .build();
    }

    /**
     * 参数错误响应
     */
    public static <T> ApiResponseVO<T> parameterError(String msgId) {
        return ApiResponseVO.<T>builder()
                .code(CodeInfo.builder()
                        .code("4000")
                        .msgId(msgId)
                        .build())
                .bo(null)
                .other(null)
                .build();
    }

    /**
     * 权限错误响应
     */
    public static <T> ApiResponseVO<T> permissionError(String msgId) {
        return ApiResponseVO.<T>builder()
                .code(CodeInfo.builder()
                        .code("4003")
                        .msgId(msgId)
                        .build())
                .bo(null)
                .other(null)
                .build();
    }
}
