package com.demo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 分类状态切换请求VO
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "分类状态切换请求VO")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CategoryStatusVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分类状态", required = true, example = "T", notes = "T=启用，F=禁用")
    @NotBlank(message = "分类状态不能为空")
    @Pattern(regexp = "^[TF]$", message = "分类状态只能是T或F")
    private String categoryStatus;
}
