package com.demo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 分类新增请求VO
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "分类新增请求VO")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CategoryCreateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分类名称", required = true, example = "计算机科学")
    @NotBlank(message = "{validation.category.name.not.blank}")
    @Size(max = 50, message = "{validation.category.name.size.max}")
    private String categoryName;

    @ApiModelProperty(value = "分类描述", required = false, example = "计算机科学相关图书")
    @Size(max = 500, message = "{validation.category.description.size.max}")
    private String categoryDescription;

    @ApiModelProperty(value = "排序号", required = false, example = "10")
    private Integer sortOrder;
}
