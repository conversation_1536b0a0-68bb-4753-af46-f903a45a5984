package com.demo.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 餐饮视图对象
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                                    // ✅ 必须：Lombok数据注解
@ApiModel(value = "餐饮活动VO")          // ✅ 必须：Swagger模型注解
@Builder                                // ✅ 推荐：构建者模式
@AllArgsConstructor                     // ✅ 推荐：全参构造函数
@NoArgsConstructor                      // ✅ 必须：无参构造函数
public class CateringVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID", required = false, example = "1001")
    private Long id;

    @ApiModelProperty(value = "餐饮活动标题", required = true, example = "团队聚餐")
    @NotBlank(message = "{validation.catering.title.not.blank}")
    @Size(max = 128, message = "{validation.catering.title.size.max}")
    private String title;

    @ApiModelProperty(value = "活动原因说明", required = false, example = "庆祝项目成功")
    @Size(max = 512, message = "{validation.catering.reason.size.max}")
    private String reason;

    @ApiModelProperty(value = "活动发生时间", required = false, example = "2024-01-15 18:30:00")
    private Date occurTime;

    @ApiModelProperty(value = "活动地点", required = false, example = "海底捞火锅店")
    @Size(max = 256, message = "{validation.catering.location.size.max}")
    private String location;

    @ApiModelProperty(value = "参与人员", required = false, example = "张三、李四、王五")
    private String participants;

    // ===== 编码转名称字段 =====
    
    @ApiModelProperty(value = "创建人姓名", required = false, example = "系统管理员")
    private String createdByName;

    @ApiModelProperty(value = "更新人姓名", required = false, example = "系统管理员")
    private String lastUpdatedByName;

    /**
     * 获取创建人姓名（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getCreatedByNameFromCode() {
        return this.createdByName;
    }

    /**
     * 获取更新人姓名（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getLastUpdatedByNameFromCode() {
        return this.lastUpdatedByName;
    }
}