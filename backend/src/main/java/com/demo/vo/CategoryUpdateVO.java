package com.demo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 分类编辑请求VO
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "分类编辑请求VO")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CategoryUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分类ID", required = true, example = "1")
    private Long oid;

    @ApiModelProperty(value = "分类名称", required = true, example = "计算机科学")
    @NotBlank(message = "{validation.category.name.not.blank}")
    @Size(max = 50, message = "{validation.category.name.size.max}")
    private String categoryName;

    @ApiModelProperty(value = "分类描述", required = false, example = "计算机科学相关图书，包括算法、数据结构等")
    @Size(max = 500, message = "{validation.category.description.size.max}")
    private String categoryDescription;

    @ApiModelProperty(value = "排序号", required = false, example = "10")
    private Integer sortOrder;

    @ApiModelProperty(value = "分类状态", required = false, example = "T")
    private String categoryStatus;
}
