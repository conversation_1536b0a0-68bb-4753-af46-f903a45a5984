package com.demo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * 图书新增视图对象
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                                    // ✅ 必须：Lombok数据注解
@ApiModel(value = "图书新增VO")          // ✅ 必须：Swagger模型注解
@Builder                                // ✅ 推荐：构建者模式
@AllArgsConstructor                     // ✅ 推荐：全参构造函数
@NoArgsConstructor                      // ✅ 必须：无参构造函数
public class BookCreateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "图书标题", required = true, example = "Java编程思想")
    @NotBlank(message = "{validation.book.title.not.blank}")
    @Size(max = 200, message = "{validation.book.title.size.max}")
    private String bookTitle;

    @ApiModelProperty(value = "图书作者", required = false, example = "<PERSON> Eckel")
    @Size(max = 300, message = "{validation.book.author.size.max}")
    @com.fasterxml.jackson.annotation.JsonProperty("author")
    private String bookAuthor;

    @ApiModelProperty(value = "ISBN编号", required = true, example = "9787111213826")
    @NotBlank(message = "{validation.book.isbn.not.blank}")
    @Size(max = 20, message = "{validation.book.isbn.size.max}")
    private String isbn;

    @ApiModelProperty(value = "分类ID", required = true, example = "1")
    @NotNull(message = "{validation.book.category.not.null}")
    private Long categoryId;

    @ApiModelProperty(value = "地区ID", required = true, example = "1")
    @NotNull(message = "{validation.book.region.not.null}")
    private Long regionId;

    @ApiModelProperty(value = "总册数", required = false, example = "5")
    @Min(value = 1, message = "{validation.book.quantity.min}")
    private Integer totalQuantity;

    @ApiModelProperty(value = "出版年份", required = false, example = "2007")
    @Min(value = 1900, message = "{validation.book.year.min}")
    @Max(value = 2100, message = "{validation.book.year.max}")
    private Integer publicationYear;

    @ApiModelProperty(value = "图书描述", required = false, example = "Java编程经典教材")
    @Size(max = 1000, message = "{validation.book.description.size.max}")
    private String bookDescription;

    @ApiModelProperty(value = "封面图片URL", required = false, example = "/images/books/java-thinking.jpg")
    @Size(max = 500, message = "{validation.book.cover.size.max}")
    private String coverImageUrl;
}
