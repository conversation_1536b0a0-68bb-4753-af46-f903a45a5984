package com.demo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 文件上传结果视图对象
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                                    // ✅ 必须：Lombok数据注解
@ApiModel(value = "文件上传结果VO")      // ✅ 必须：Swagger模型注解
@Builder                                // ✅ 推荐：构建者模式
@AllArgsConstructor                     // ✅ 推荐：全参构造函数
@NoArgsConstructor                      // ✅ 必须：无参构造函数
public class FileUploadResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "文件访问URL", required = true, example = "/images/books/java-thinking-20240115.jpg")
    private String fileUrl;

    @ApiModelProperty(value = "文件名", required = true, example = "java-thinking-20240115.jpg")
    private String fileName;

    @ApiModelProperty(value = "文件大小（字节）", required = true, example = "524288")
    private Long fileSize;

    @ApiModelProperty(value = "上传时间", required = true, example = "2024-01-15 10:30:00")
    private String uploadTime;
}
