package com.demo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * 图书编辑请求VO
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "图书编辑请求VO")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BookUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "图书ID", required = true, example = "1")
    private Long oid;

    @ApiModelProperty(value = "图书标题", required = true, example = "Java编程思想（第4版）")
    @NotBlank(message = "{validation.book.title.not.blank}")
    @Size(max = 200, message = "{validation.book.title.size.max}")
    private String bookTitle;

    @ApiModelProperty(value = "图书作者", required = false, example = "Bruce E<PERSON>l")
    @Size(max = 300, message = "{validation.book.author.size.max}")
    @com.fasterxml.jackson.annotation.JsonProperty("author")
    private String bookAuthor;

    @ApiModelProperty(value = "ISBN编号", required = true, example = "9787111213826")
    @NotBlank(message = "{validation.book.isbn.not.blank}")
    @Size(max = 20, message = "{validation.book.isbn.size.max}")
    private String isbn;

    @ApiModelProperty(value = "分类ID", required = true, example = "1")
    @NotNull(message = "{validation.book.category.not.null}")
    private Long categoryId;

    @ApiModelProperty(value = "地区ID", required = true, example = "1")
    @NotNull(message = "{validation.book.region.not.null}")
    private Long regionId;

    @ApiModelProperty(value = "总册数", required = true, example = "5")
    @NotNull(message = "{validation.book.quantity.min}")
    @Min(value = 1, message = "{validation.book.quantity.min}")
    private Integer totalQuantity;

    @ApiModelProperty(value = "出版年份", required = false, example = "2007")
    @Min(value = 1900, message = "{validation.book.year.min}")
    @Max(value = 2100, message = "{validation.book.year.max}")
    private Integer publicationYear;

    @ApiModelProperty(value = "图书描述", required = false, example = "Java编程经典教材，第4版新增内容")
    @Size(max = 1000, message = "{validation.book.description.size.max}")
    private String bookDescription;

    @ApiModelProperty(value = "封面图片URL", required = false, example = "/images/books/java-thinking-4th.jpg")
    @Size(max = 500, message = "{validation.book.cover.size.max}")
    private String coverImageUrl;
}
