package com.demo.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 地区信息视图对象
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                                    // ✅ 必须：Lombok数据注解
@ApiModel(value = "地区信息VO")          // ✅ 必须：Swagger模型注解
@Builder                                // ✅ 推荐：构建者模式
@AllArgsConstructor                     // ✅ 推荐：全参构造函数
@NoArgsConstructor                      // ✅ 必须：无参构造函数
public class RegionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "地区ID", required = false, example = "1")
    private Long oid;

    @ApiModelProperty(value = "地区名称", required = true, example = "总馆")
    @NotBlank(message = "{validation.region.name.not.blank}")
    @Size(max = 100, message = "{validation.region.name.size.max}")
    private String regionName;

    @ApiModelProperty(value = "地区地址", required = true, example = "北京市海淀区中关村南大街1号")
    @NotBlank(message = "{validation.region.address.not.blank}")
    @Size(max = 200, message = "{validation.region.address.size.max}")
    private String regionAddress;

    @ApiModelProperty(value = "联系电话", required = false, example = "010-12345678")
    @Size(max = 20, message = "{validation.region.phone.size.max}")
    private String contactPhone;

    @ApiModelProperty(value = "地区描述", required = false, example = "图书管理系统总馆，负责全系统的统一管理")
    @Size(max = 500, message = "{validation.region.description.size.max}")
    private String regionDescription;

    @ApiModelProperty(value = "排序号", required = false, example = "1")
    private Integer sortOrder;

    @ApiModelProperty(value = "地区状态", required = false, example = "T")
    private String regionStatus;

    @ApiModelProperty(value = "是否预设地区", required = false, example = "T")
    private String isPreset;

    @ApiModelProperty(value = "关联图书数量", required = false, example = "100")
    private Integer bookCount;

    @ApiModelProperty(value = "创建时间", required = false, example = "2024-01-15 10:00:00")
    private Date createdDate;

    // ===== 编码转名称字段 =====
    
    @ApiModelProperty(value = "地区状态名称", required = false, example = "启用")
    private String regionStatusName;

    @ApiModelProperty(value = "预设标识名称", required = false, example = "是")
    private String isPresetName;

    @ApiModelProperty(value = "创建人姓名", required = false, example = "系统管理员")
    private String createdByName;

    @ApiModelProperty(value = "更新人姓名", required = false, example = "系统管理员")
    private String lastUpdatedByName;

    /**
     * 获取地区状态名称（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getRegionStatusNameFromCode() {
        return this.regionStatusName;
    }

    /**
     * 获取预设标识名称（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getIsPresetNameFromCode() {
        return this.isPresetName;
    }

    /**
     * 获取创建人姓名（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getCreatedByNameFromCode() {
        return this.createdByName;
    }

    /**
     * 获取更新人姓名（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getLastUpdatedByNameFromCode() {
        return this.lastUpdatedByName;
    }
}
