package com.demo.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 图书信息视图对象
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data                                    // ✅ 必须：Lombok数据注解
@ApiModel(value = "图书信息VO")          // ✅ 必须：Swagger模型注解
@Builder                                // ✅ 推荐：构建者模式
@AllArgsConstructor                     // ✅ 推荐：全参构造函数
@NoArgsConstructor                      // ✅ 必须：无参构造函数
public class BookVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "图书ID", required = false, example = "1")
    private Long oid;

    @ApiModelProperty(value = "图书标题", required = true, example = "Java编程思想")
    @NotBlank(message = "{validation.book.title.not.blank}")
    @Size(max = 200, message = "{validation.book.title.size.max}")
    private String bookTitle;

    @ApiModelProperty(value = "图书作者", required = false, example = "Bruce Eckel")
    @Size(max = 300, message = "{validation.book.author.size.max}")
    private String bookAuthor;

    @ApiModelProperty(value = "ISBN编号", required = true, example = "9787111213826")
    @NotBlank(message = "{validation.book.isbn.not.blank}")
    @Size(max = 20, message = "{validation.book.isbn.size.max}")
    private String isbn;

    @ApiModelProperty(value = "分类ID", required = true, example = "1")
    @NotNull(message = "{validation.book.category.not.null}")
    private Long categoryId;

    @ApiModelProperty(value = "地区ID", required = true, example = "1")
    @NotNull(message = "{validation.book.region.not.null}")
    private Long regionId;

    @ApiModelProperty(value = "总册数", required = false, example = "5")
    @Min(value = 1, message = "{validation.book.quantity.min}")
    private Integer totalQuantity;

    @ApiModelProperty(value = "可借册数", required = false, example = "3")
    private Integer availableQuantity;

    @ApiModelProperty(value = "已借册数", required = false, example = "2")
    private Integer borrowedQuantity;

    @ApiModelProperty(value = "出版年份", required = false, example = "2007")
    @Min(value = 1900, message = "{validation.book.year.min}")
    @Max(value = 2100, message = "{validation.book.year.max}")
    private Integer publicationYear;

    @ApiModelProperty(value = "图书描述", required = false, example = "Java编程经典教材")
    @Size(max = 1000, message = "{validation.book.description.size.max}")
    private String bookDescription;

    @ApiModelProperty(value = "封面图片URL", required = false, example = "/images/books/java-thinking.jpg")
    @Size(max = 500, message = "{validation.book.cover.size.max}")
    private String coverImageUrl;

    @ApiModelProperty(value = "图书状态", required = false, example = "AVAILABLE")
    private String bookStatus;

    @ApiModelProperty(value = "创建人", required = false, example = "admin")
    private String createdBy;

    @ApiModelProperty(value = "创建时间", required = false, example = "2024-01-15 10:30:00")
    private Date createdDate;

    @ApiModelProperty(value = "更新人", required = false, example = "admin")
    private String lastUpdatedBy;

    @ApiModelProperty(value = "更新时间", required = false, example = "2024-01-15 10:30:00")
    private Date lastUpdatedDate;

    // ===== 编码转名称字段 =====
    
    @ApiModelProperty(value = "分类名称", required = false, example = "科学技术")
    private String categoryName;

    @ApiModelProperty(value = "地区名称", required = false, example = "总馆")
    private String regionName;

    @ApiModelProperty(value = "图书状态名称", required = false, example = "可借阅")
    private String bookStatusName;

    @ApiModelProperty(value = "创建人姓名", required = false, example = "系统管理员")
    private String createdByName;

    @ApiModelProperty(value = "更新人姓名", required = false, example = "系统管理员")
    private String lastUpdatedByName;

    /**
     * 获取分类名称（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getCategoryNameFromCode() {
        return this.categoryName;
    }

    /**
     * 获取地区名称（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getRegionNameFromCode() {
        return this.regionName;
    }

    /**
     * 获取图书状态名称（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getBookStatusNameFromCode() {
        return this.bookStatusName;
    }

    /**
     * 获取创建人姓名（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getCreatedByNameFromCode() {
        return this.createdByName;
    }

    /**
     * 获取更新人姓名（实际逻辑在Service层实现）
     * 注意：此方法仅作为占位符，实际的批量查询逻辑在Service层实现
     */
    @JsonIgnore
    public String getLastUpdatedByNameFromCode() {
        return this.lastUpdatedByName;
    }
}
