package com.demo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 图书删除结果VO
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "图书删除结果VO")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BookDeleteResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "删除的图书ID", required = true, example = "1")
    private Long oid;

    @ApiModelProperty(value = "图书标题", required = true, example = "Java编程思想")
    private String bookTitle;

    @ApiModelProperty(value = "是否删除成功", required = true, example = "true")
    private Boolean isDeleted;

    @ApiModelProperty(value = "影响的行数", required = true, example = "1")
    private Integer affectedRows;

    @ApiModelProperty(value = "删除时间", required = false, example = "2024-01-15 10:30:00")
    private String deleteTime;
}
