package com.demo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 图书批量删除请求VO
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(value = "图书批量删除请求VO")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BookBatchDeleteVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "图书ID列表", required = true, example = "[1, 2, 3]")
    @NotEmpty(message = "图书ID列表不能为空")
    @Size(max = 100, message = "单次删除图书数量不能超过100本")
    private List<@NotNull Long> bookIds;
}
