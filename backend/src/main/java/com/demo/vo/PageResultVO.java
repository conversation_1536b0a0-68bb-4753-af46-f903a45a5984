package com.demo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 分页查询结果VO
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageResultVO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据列表
     */
    private List<T> records;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 每页大小
     */
    private Long size;

    /**
     * 当前页码
     */
    private Long current;

    /**
     * 总页数
     */
    private Long pages;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 构建分页结果
     */
    public static <T> PageResultVO<T> build(List<T> records, Long total, Long current, Long size) {
        Long pages = (total + size - 1) / size;
        
        return PageResultVO.<T>builder()
                .records(records)
                .total(total)
                .size(size)
                .current(current)
                .pages(pages)
                .hasPrevious(current > 1)
                .hasNext(current < pages)
                .build();
    }

    /**
     * 空结果
     */
    public static <T> PageResultVO<T> empty() {
        return PageResultVO.<T>builder()
                .records(new ArrayList<>())
                .total(0L)
                .size(10L)
                .current(1L)
                .pages(0L)
                .hasPrevious(false)
                .hasNext(false)
                .build();
    }

    /**
     * 空结果（指定分页参数）
     */
    public static <T> PageResultVO<T> empty(Long current, Long size) {
        return PageResultVO.<T>builder()
                .records(new ArrayList<>())
                .total(0L)
                .size(size)
                .current(current)
                .pages(0L)
                .hasPrevious(false)
                .hasNext(false)
                .build();
    }
}
