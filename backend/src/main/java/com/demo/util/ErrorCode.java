package com.demo.util;

/**
 * 错误码常量定义
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class ErrorCode {

    // ===== 系统相关错误码 =====
    public static final String SUCCESS = "0000";
    public static final String SYSTEM_ERROR = "9999";
    public static final String SYSTEM_BUSY_ERROR = "9998";
    public static final String PARAMETER_ERROR = "4000";
    public static final String NO_PERMISSION = "4003";
    public static final String DATA_NOT_FOUND = "4004";

    // ===== 图书相关错误码 =====
    public static final String BOOK_NOT_FOUND = "B001";
    public static final String ISBN_ALREADY_EXISTS = "B002";
    public static final String BOOK_HAS_BORROW_RECORDS = "B003";
    public static final String BOOK_CATEGORY_NOT_AVAILABLE = "B004";
    public static final String BOOK_REGION_NOT_AVAILABLE = "B005";

    // ===== 分类相关错误码 =====
    public static final String CATEGORY_NOT_FOUND = "C001";
    public static final String CATEGORY_NAME_ALREADY_EXISTS = "C002";
    public static final String CATEGORY_HAS_BOOKS = "C003";
    public static final String CATEGORY_PRESET_CANNOT_DELETE = "C004";

    // ===== 地区相关错误码 =====
    public static final String REGION_NOT_FOUND = "R001";
    public static final String REGION_NAME_ALREADY_EXISTS = "R002";
    public static final String REGION_HAS_BOOKS = "R003";
    public static final String REGION_PRESET_CANNOT_DELETE = "R004";

    // ===== 文件相关错误码 =====
    public static final String FILE_SIZE_EXCEED_LIMIT = "F001";
    public static final String FILE_FORMAT_NOT_SUPPORTED = "F002";
    public static final String FILE_UPLOAD_FAILED = "F003";
    public static final String FILE_READ_ERROR = "F004";
    public static final String IMAGE_SIZE_NOT_SUPPORTED = "F005";

    // ===== 业务操作错误码 =====
    public static final String OPERATION_FAILED = "O001";
    public static final String BATCH_OPERATION_FAILED = "O002";
    public static final String DATA_CONFLICT = "O003";
    public static final String VALIDATION_FAILED = "O004";

    /**
     * 私有构造函数，防止实例化
     */
    private ErrorCode() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
