package com.demo.util;

import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 对象属性复制工具类
 * 统一使用CopyUtil进行对象转换，禁止使用其他方式
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class CopyUtil {

    /**
     * 单对象属性复制
     * 
     * @param source 源对象
     * @param target 目标对象
     */
    public static void copyProperties(Object source, Object target) {
        if (source == null || target == null) {
            return;
        }
        
        try {
            BeanUtils.copyProperties(source, target);
        } catch (Exception e) {
            throw new RuntimeException("对象属性复制失败", e);
        }
    }

    /**
     * 单对象转换（创建新实例）
     * 
     * @param source 源对象
     * @param targetClass 目标类型
     * @param <T> 目标类型
     * @return 目标对象实例
     */
    public static <T> T copyProperties(Object source, Class<T> targetClass) {
        if (source == null || targetClass == null) {
            return null;
        }
        
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("对象属性复制失败: " + e.getMessage(), e);
        }
    }

    /**
     * 集合对象属性复制
     * 
     * @param sourceList 源对象集合
     * @param targetClass 目标类型
     * @param <S> 源类型
     * @param <T> 目标类型
     * @return 目标对象集合
     */
    public static <S, T> List<T> copyListProperties(List<S> sourceList, Class<T> targetClass) {
        if (CollectionUtils.isEmpty(sourceList) || targetClass == null) {
            return Collections.emptyList();
        }
        
        try {
            return sourceList.stream()
                    .map(source -> copyProperties(source, targetClass))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("集合对象属性复制失败: " + e.getMessage(), e);
        }
    }

    /**
     * 集合对象属性复制（使用ArrayList）
     * 
     * @param sourceList 源对象集合
     * @param targetClass 目标类型
     * @param <S> 源类型
     * @param <T> 目标类型
     * @return 目标对象集合（ArrayList）
     */
    public static <S, T> ArrayList<T> copyArrayListProperties(List<S> sourceList, Class<T> targetClass) {
        if (CollectionUtils.isEmpty(sourceList) || targetClass == null) {
            return new ArrayList<>();
        }
        
        try {
            return sourceList.stream()
                    .map(source -> copyProperties(source, targetClass))
                    .collect(Collectors.toCollection(ArrayList::new));
        } catch (Exception e) {
            throw new RuntimeException("集合对象属性复制失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查源对象是否为空
     * 
     * @param source 源对象
     * @return 是否为空
     */
    public static boolean isEmpty(Object source) {
        return source == null;
    }

    /**
     * 检查源集合是否为空
     * 
     * @param sourceList 源集合
     * @return 是否为空
     */
    public static boolean isEmpty(List<?> sourceList) {
        return CollectionUtils.isEmpty(sourceList);
    }
}
