package com.demo.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * JWT Token工具类
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Component
@Slf4j
public class JwtTokenUtil {

    @Value("${jwt.secret:bookManagementSecret}")
    private String secret;

    @Value("${jwt.expiration:3600}")
    private Long expiration;

    /**
     * 从token中获取用户名
     */
    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    /**
     * 从token中获取过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    /**
     * 从token中获取指定claim
     */
    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }

    /**
     * 从token中获取所有claims
     */
    private Claims getAllClaimsFromToken(String token) {
        try {
            return Jwts.parser()
                    .setSigningKey(secret)
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            log.warn("解析JWT token失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查token是否过期
     */
    private Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration == null || expiration.before(new Date());
    }

    /**
     * 生成token
     */
    public String generateToken(String username, String role, String tenantId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("role", role);
        claims.put("tenantId", tenantId);
        return createToken(claims, username);
    }

    /**
     * 创建token
     */
    private String createToken(Map<String, Object> claims, String subject) {
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expiration * 1000))
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }

    /**
     * 验证token
     */
    public Boolean validateToken(String token, String username) {
        if (token == null || username == null) {
            return false;
        }
        
        try {
            final String tokenUsername = getUsernameFromToken(token);
            return (username.equals(tokenUsername) && !isTokenExpired(token));
        } catch (Exception e) {
            log.warn("验证JWT token失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证token（仅验证格式和过期时间）
     */
    public Boolean validateToken(String token) {
        try {
            return token != null && !isTokenExpired(token) && getUsernameFromToken(token) != null;
        } catch (Exception e) {
            log.warn("验证JWT token失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取token过期时间（秒）
     */
    public Long getExpirationTime() {
        return expiration;
    }

    /**
     * 从token中获取租户ID
     */
    public String getTenantIdFromToken(String token) {
        try {
            Claims claims = getAllClaimsFromToken(token);
            return claims != null ? (String) claims.get("tenantId") : null;
        } catch (Exception e) {
            log.warn("从token获取租户ID失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从token中获取角色
     */
    public String getRoleFromToken(String token) {
        try {
            Claims claims = getAllClaimsFromToken(token);
            return claims != null ? (String) claims.get("role") : null;
        } catch (Exception e) {
            log.warn("从token获取角色失败: {}", e.getMessage());
            return null;
        }
    }
}
