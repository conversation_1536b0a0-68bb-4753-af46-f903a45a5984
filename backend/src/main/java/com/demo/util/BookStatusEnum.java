package com.demo.util;

/**
 * 图书状态枚举
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public enum BookStatusEnum {

    AVAILABLE("AVAILABLE", "可借阅", "Available"),
    UNAVAILABLE("UNAVAILABLE", "不可借阅", "Unavailable"),
    DAMAGED("DAMAGED", "已损坏", "Damaged"),
    LOST("LOST", "已遗失", "Lost");

    private final String code;
    private final String chineseDesc;
    private final String englishDesc;

    BookStatusEnum(String code, String chineseDesc, String englishDesc) {
        this.code = code;
        this.chineseDesc = chineseDesc;
        this.englishDesc = englishDesc;
    }

    public String getCode() {
        return code;
    }

    public String getChineseDesc() {
        return chineseDesc;
    }

    public String getEnglishDesc() {
        return englishDesc;
    }

    /**
     * 根据编码获取描述
     * 
     * @param code 状态编码
     * @param isEnglish 是否为英文
     * @return 状态描述
     */
    public static String getDesc(String code, boolean isEnglish) {
        if (code == null) {
            return "";
        }
        
        for (BookStatusEnum status : BookStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return isEnglish ? status.getEnglishDesc() : status.getChineseDesc();
            }
        }
        return code;
    }
}
