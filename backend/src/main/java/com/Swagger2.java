package com;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.builders.RequestParameterBuilder;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ParameterType;
import springfox.documentation.service.RequestParameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableSwagger2
public class Swagger2 {

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.OAS_30)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com"))
                .paths(PathSelectors.any())
                .build().globalRequestParameters(globalRequestParameters());
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("API管理")
                .description("微服务cursordemo接口管理")
                .version("1.0")
                .build();
    }

    private List<RequestParameter> globalRequestParameters(){
        RequestParameterBuilder token = new RequestParameterBuilder();
        RequestParameterBuilder userid = new RequestParameterBuilder();
        RequestParameterBuilder lang = new RequestParameterBuilder();
        RequestParameterBuilder orgid = new RequestParameterBuilder();
        RequestParameterBuilder tenantid = new RequestParameterBuilder();
        RequestParameterBuilder servicename = new RequestParameterBuilder();
        RequestParameterBuilder itpValue = new RequestParameterBuilder();
        RequestParameterBuilder interfaceKey = new RequestParameterBuilder();

        List<RequestParameter> pars = new ArrayList<>();
        //所有接口默认请求头。框架默认都是非必须，应根据实际情况设置每个头的 required 属性来指明是否必须项。
        token.name("X-Auth-Value").description("token值").in(ParameterType.HEADER).required(false);
        userid.name("X-Emp-No").description("员工短工号").in(ParameterType.HEADER).required(false);
        lang.name("X-Lang-Id").description("语言标准编码").in(ParameterType.HEADER).required(false);
        orgid.name("X-Org-Id").description("多租房下的组织ID").in(ParameterType.HEADER).required(false);
        tenantid.name("X-Tenant-Id").description("租户编号").in(ParameterType.HEADER).required(false);
        servicename.name("X-Origin-ServiceName").description("调用方微服务名").in(ParameterType.HEADER).required(false);
        itpValue.name("X-Itp-Value").description("客户端设备的相关信息").in(ParameterType.HEADER).required(false);
        interfaceKey.name("X_Interface_Key").description("调用接口权限key").in(ParameterType.HEADER).required(false);

        pars.add(token.build());
        pars.add(userid.build());
        pars.add(lang.build());
        pars.add(orgid.build());
        pars.add(tenantid.build());
        pars.add(servicename.build());
        pars.add(itpValue.build());
        pars.add(interfaceKey.build());

        return pars;
    }

}
