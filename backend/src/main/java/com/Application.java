package com;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

import java.util.TimeZone;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class Application {

    private static final Logger logger = LoggerFactory.getLogger(Application.class);

    public static void main(String[] args) {

        try {
            // 启动springboot
            SpringApplication.run(Application.class, args);
            initEnv();
            logger.info("cursordemo start success!");
        } catch (Throwable e) {
            // 控制台和logback日志都记录一下。
            logger.error("Application Start Failed", e);
        }
    }
    /**
     * 初始化环境
     */
    private static void initEnv() {
        System.setProperty("user.timezone", "Asia/Shanghai");
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
    }

}
