spring:
  application:
    name: cursordemo
    type: cursordemo
  profiles:
    #格式 环境[dev|prod]，配置版本号
    #配置中心会根据这里的配置，到对应的目录(dev或v1.0.1目录)下搜索本服务的配置文件
    #dev仅用来声明运行环境是开发环境，请不要在该目录下放配置文件。
    #v1.0.1为根据版本号定义，如果配置文件有更改建议同时更改版本号，使用不同的配置文件目录。
    #loc 是指在本地调试时，如果配置中心未启用，可先到本地 application-loc.properties获取。
    active: loc,dev,v1.0.0
  cloud:
    config:
      #配置中心地址
      #1、开发环境  http://devconfig.test.zte.com.cn/it-config-server
      #2、系统测试环境 http://stconfig.test.zte.com.cn/it-config-server
      #3、生产环境 http://itconfig.zte.com.cn/it-config-server
      #如下配置中心地址默认注释掉了，如果已经在配置中心提交了配置文件则设置为正确的地址即可启用

      #uri: http://stconfig.test.zte.com.cn/it-config-server

      #配置中心库名，使用微服务名的前三段，例如 zte-itp-isoa
      label: ${spring.application.type}

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

msa:
  rootkey:
    salt:
      12345678