<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.demo.mapper.CateringMapper">

    <!-- 通用结果映射 -->
    <resultMap id="cateringPOResultMap" type="com.demo.po.CateringPO">
        <id property="id" column="id" />
        <result property="title" column="title" />
        <result property="reason" column="reason" />
        <result property="occurTime" column="occur_time" />
        <result property="location" column="location" />
        <result property="participants" column="participants" />
        <result property="createdBy" column="created_by" />
        <result property="createdDate" column="created_date" />
        <result property="lastUpdatedBy" column="last_updated_by" />
        <result property="lastUpdatedDate" column="last_updated_date" />
        <result property="tenantId" column="tenant_id" />
        <result property="enableFlag" column="enable_flag" />
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, title, reason, occur_time, location, participants,
        created_by, created_date, last_updated_by, last_updated_date, tenant_id, enable_flag
    </sql>

    <!-- MyBatis-Plus BaseMapper 基础方法 -->
    
    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="cateringPOResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM catering
        WHERE id = #{id}
    </select>
    
    <!-- 插入 -->
    <insert id="insert" parameterType="com.demo.po.CateringPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO catering (
            title, reason, occur_time, location, participants,
            created_by, created_date, last_updated_by, last_updated_date, tenant_id, enable_flag
        ) VALUES (
            #{title}, #{reason}, #{occurTime}, #{location}, #{participants},
            #{createdBy}, #{createdDate}, #{lastUpdatedBy}, #{lastUpdatedDate}, #{tenantId}, #{enableFlag}
        )
    </insert>
    
    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="com.demo.po.CateringPO">
        UPDATE catering
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="occurTime != null">occur_time = #{occurTime},</if>
            <if test="location != null">location = #{location},</if>
            <if test="participants != null">participants = #{participants},</if>
            <if test="lastUpdatedBy != null">last_updated_by = #{lastUpdatedBy},</if>
            <if test="lastUpdatedDate != null">last_updated_date = #{lastUpdatedDate},</if>
            <if test="enableFlag != null">enable_flag = #{enableFlag},</if>
        </set>
        WHERE id = #{id}
    </update>
    
    <!-- 根据ID删除（物理删除，一般不使用） -->
    <delete id="deleteById">
        DELETE FROM catering WHERE id = #{id}
    </delete>

    <!-- 根据租户ID分页查询有效记录 -->
    <select id="selectPageByTenant" resultMap="cateringPOResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM catering
        WHERE tenant_id = #{tenantId}
          AND enable_flag = #{enableFlag}
        ORDER BY created_date DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 根据租户ID统计有效记录数 -->
    <select id="countByTenant" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM catering
        WHERE tenant_id = #{tenantId}
          AND enable_flag = #{enableFlag}
    </select>

    <!-- 根据标题模糊查询 -->
    <select id="selectByTitleLike" resultMap="cateringPOResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM catering
        WHERE tenant_id = #{tenantId}
          AND enable_flag = #{enableFlag}
          AND title LIKE CONCAT('%', #{title}, '%')
        ORDER BY created_date DESC
    </select>

    <!-- 批量逻辑删除 -->
    <update id="batchLogicDelete">
        UPDATE catering
        SET enable_flag = 'F',
            last_updated_by = #{lastUpdatedBy},
            last_updated_date = NOW()
        WHERE tenant_id = #{tenantId}
          AND id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>