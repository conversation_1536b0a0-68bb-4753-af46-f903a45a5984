<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.demo.mapper.RegionMapper">

    <!-- 结果映射 -->
    <resultMap id="RegionVOResultMap" type="com.demo.vo.RegionVO">
        <id column="oid" property="oid"/>
        <result column="region_name" property="regionName"/>
        <result column="region_address" property="regionAddress"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="region_description" property="regionDescription"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="region_status" property="regionStatus"/>
        <result column="is_preset" property="isPreset"/>
        <result column="book_count" property="bookCount"/>
        <result column="created_date" property="createdDate"/>
    </resultMap>

    <!-- 查询所有地区列表（带图书数量统计） -->
    <select id="selectRegionListWithBookCount" resultMap="RegionVOResultMap">
        SELECT 
            r.oid,
            r.region_name,
            r.region_address,
            r.contact_phone,
            r.region_description,
            r.sort_order,
            r.region_status,
            r.is_preset,
            r.created_date,
            COALESCE(b.book_count, 0) as book_count
        FROM regions r
        LEFT JOIN (
            SELECT 
                region_id,
                COUNT(*) as book_count
            FROM books 
            WHERE enable_flag = 'T'
            GROUP BY region_id
        ) b ON r.oid = b.region_id
        WHERE r.enable_flag = 'T'
        ORDER BY r.sort_order ASC, r.created_date DESC
    </select>

    <!-- 批量查询地区名称 -->
    <select id="batchQueryRegionNames" resultType="java.util.Map">
        SELECT 
            oid as `key`,
            region_name as `value`
        FROM regions
        WHERE enable_flag = 'T'
        AND region_status = 'T'
        <if test="regionIds != null and regionIds.size() > 0">
            AND oid IN
            <foreach collection="regionIds" item="regionId" open="(" separator="," close=")">
                #{regionId}
            </foreach>
        </if>
    </select>

    <!-- 检查地区名称是否存在 -->
    <select id="checkRegionNameExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM regions
        WHERE enable_flag = 'T'
        AND region_name = #{regionName}
        <if test="excludeId != null">
            AND oid != #{excludeId}
        </if>
    </select>

    <!-- 获取地区关联的图书数量 -->
    <select id="getBookCountByRegionId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM books
        WHERE enable_flag = 'T'
        AND region_id = #{regionId}
    </select>

</mapper>
