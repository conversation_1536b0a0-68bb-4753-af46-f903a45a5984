#==============================注册中心配置=============================
#配置中心所属环境
register.address=************:10081
#注册到msb的ip
register.node.ip=***********
#注册到msb的端口
register.node.port=8473
#注册到msb的版本
register.version=v10292883
#注册中心类型 默认是eureka，所以使用msb模式需要指定
servicecenter=msb
#如果不是依赖msa-starter-eureka 可以设置下面这个关闭属性
eureka.client.enabled=false


#==============================数据库配置=============================
jasypt.encryptor.password=eIrCQS/uxNmggDv0etN6cH5ogRNs2IhDxcRuxVQg9z/6qwdV/Fen8VebeYfgff4teEjnPOE65n1klZp9
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
jdbc1.type=com.alibaba.druid.pool.DruidDataSource
jdbc1.driverClassName=com.mysql.jdbc.Driver
jdbc1.url=***************************************************************************************************************************************************************************************************************************************
jdbc1.username=x3RLXGEza3JICVCHkc0KkWzJhP0HyZYvcUEt4X9Na28=
jdbc1.password=LIRq3yxdhBsR6ScnWb26Jew1qgGoURwL+TyuPlCkJzHxlKdOE5UxwQ==

jdbc1.initialSize = 5
jdbc1.minIdle = 5
jdbc1.maxActive = 20
jdbc1.maxWait = 60000
jdbc1.validationQuery = SELECT 1 FROM DUAL
jdbc1.testOnBorrow = false
jdbc1.testOnReturn = false
jdbc1.testWhileIdle = true
jdbc1.minEvictableIdleTimeMillis = 1200000
jdbc1.timeBetweenEvictionRunsMillis = 60000
jdbc1.removeAbandoned = true
jdbc1.removeAbandonedTimeout = 3600
jdbc1.poolPreparedStatements = true
jdbc1.maxPoolPreparedStatementPerConnectionSize = 20
jdbc1.logAbandoned = true
jdbc1.filters = stat,wall,log4j

spring.jackson.deserialization.fail-on-unknown-properties=false