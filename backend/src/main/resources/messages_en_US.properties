RetCode.Success=Success
RetCode.ServerError=Server Error
RetCode.AuthFailed=Auth Failed
RetCode.PermissionDenied=Permission Denied
RetCode.ValidationError=Validation Error
RetCode.BusinessError=Business Error

# Catering module validation messages
validation.catering.title.not.blank=Catering activity title cannot be blank
validation.catering.title.size.max=Catering activity title length cannot exceed 128 characters
validation.catering.reason.size.max=Activity reason length cannot exceed 512 characters
validation.catering.location.size.max=Activity location length cannot exceed 256 characters

# Book Management Validation Messages
validation.book.title.not.blank=Book title cannot be blank
validation.book.title.size.max=Book title length cannot exceed 200 characters
validation.book.author.size.max=Book author length cannot exceed 300 characters
validation.book.isbn.not.blank=ISBN cannot be blank
validation.book.isbn.size.max=ISBN length cannot exceed 20 characters
validation.book.category.not.null=Category cannot be null
validation.book.region.not.null=Region cannot be null
validation.book.quantity.min=Quantity must be greater than 0
validation.book.year.min=Publication year cannot be earlier than 1900
validation.book.year.max=Publication year cannot be later than 2100
validation.book.description.size.max=Book description length cannot exceed 1000 characters
validation.book.cover.size.max=Cover image URL length cannot exceed 500 characters

# Category Management Validation Messages
validation.category.name.not.blank=Category name cannot be blank
validation.category.name.size.max=Category name length cannot exceed 50 characters
validation.category.description.size.max=Category description length cannot exceed 500 characters

# Region Management Validation Messages
validation.region.name.not.blank=Region name cannot be blank
validation.region.name.size.max=Region name length cannot exceed 100 characters
validation.region.address.not.blank=Region address cannot be blank
validation.region.address.size.max=Region address length cannot exceed 200 characters
validation.region.phone.size.max=Contact phone length cannot exceed 20 characters
validation.region.description.size.max=Region description length cannot exceed 500 characters

# Query Validation Messages
validation.search.keyword.size.max=Search keyword length cannot exceed 50 characters
validation.page.num.min=Page number must be greater than 0
validation.page.size.min=Page size must be greater than 0
validation.page.size.max=Page size cannot exceed 100
validation.category.id.min=Category ID must be greater than 0
validation.region.id.min=Region ID must be greater than 0

# File Upload Validation Messages
validation.file.not.null=File cannot be null

# Users module validation messages
validation.users.username.not.blank=Username cannot be blank
validation.users.username.size.max=Username length cannot exceed 32 characters

# Categories module validation messages
validation.categories.category_name.not.blank=Category name cannot be blank
validation.categories.category_name.size.max=Category name length cannot exceed 50 characters
validation.categories.description.size.max=Category description length cannot exceed 200 characters

# Authentication module validation messages
validation.auth.username.not.blank=Username cannot be blank
validation.auth.password.not.blank=Password cannot be blank

# Regions module validation messages
validation.regions.region_name.not.blank=Region name cannot be blank
validation.regions.region_name.size.max=Region name length cannot exceed 100 characters
validation.regions.address.not.blank=Address cannot be blank
validation.regions.address.size.max=Address length cannot exceed 200 characters
validation.regions.phone.format.invalid=Phone number format is incorrect
validation.regions.phone.size.max=Phone number length cannot exceed 20 characters
validation.regions.description.size.max=Region description length cannot exceed 200 characters

# Books module validation messages
validation.books.book_name.not.blank=Book name cannot be blank
validation.books.book_name.size.max=Book name length cannot exceed 200 characters
validation.books.author.not.blank=Author cannot be blank
validation.books.author.size.max=Author length cannot exceed 100 characters
validation.books.isbn.not.blank=ISBN cannot be blank
validation.books.isbn.size.max=ISBN length cannot exceed 20 characters
validation.books.category_id.not.null=Category ID cannot be null
validation.books.region_id.not.null=Region ID cannot be null
validation.books.total_copies.not.null=Total copies cannot be null
validation.books.total_copies.min=Total copies must be greater than 0
validation.books.publication_year.not.null=Publication year cannot be null
validation.books.publication_year.min=Publication year must be after 1900
validation.books.publication_year.max=Publication year cannot exceed 2030
validation.books.description.size.max=Book description length cannot exceed 1000 characters
validation.books.keyword.size.max=Search keyword length cannot exceed 200 characters