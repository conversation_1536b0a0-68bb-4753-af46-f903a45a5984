<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义文件内容的输出格式 -->
    <property name="PATTERN" value="%-12(%d{yyyy-MM-dd HH:mm:ss.SSS}) |-%-5level [%thread] %c [%L] -| %msg%n"/>
    <!-- 定义生产代码日志文件路径 -->
    <property name="LOG_PATH" value="/usr/local/tomcat/logs"/>
    <!-- 务必使用你的微服名 -->
    <springProperty scope="context" name="spring.application.name" source="spring.application.name"
                    defaultValue="noservicename"/>
	<property name="spring.application.name" value="cursordemo"/> <!-- 这里请务必修改成你的微服务名 -->
    <!-- 务必修改topic，格式：ms_zte_领域_app,有效topic参考WIKI：https://wiki.zte.com.cn/pages/viewpage.action?pageId=341289787 -->
    <property name="isoalog.kafka.topic" value="ms-zte-fin-app"/> <!-- 日志输送到KAFKA时所需要的topic -->
    <!-- ContenxtName必需设定为微服务名 -->
    <contextName>${spring.application.name}</contextName>

    <!-- 注意建议把频繁打印日志的第三方包设置成WARN级别 -->
    <logger name="springfox" level="WARN"/>
    <logger name="org.springframework.web.servlet" level="WARN"/>
    <logger name="com.navercorp" level="WARN"/>
    <logger name="org.springframework.jmx" level="WARN"/>
    <logger name="org.springframework.boot.actuate.endpoint.jmx" level="WARN"/>
    <logger name="org.springframework.jmx" level="WARN"/>
    <logger name="com.ulisesbocchio.jasyptspringboot" level="WARN"/>
    <logger name="redis.clients.jedis" level="WARN"/>
    <logger name="com.navercorp.pinpoint.profiler.context.AgentMetaDataHelper" level="WARN"/>
    <logger name="org.apache.kafka.clients.producer.ProducerConfig" level="WARN"/>
    <logger name="org.apache.kafka.clients.consumer.ConsumerConfig" level="WARN"/>
    <logger name="org.apache.kafka.common.config.AbstractConfig" level="WARN"/>
    <logger name="com.zte.itp.msa.datasource.health.MsaDataSourceUtil" level="ERROR"/>

    <logger name="com" level="INFO"/>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 控制台日志输出  Filter会在运行几分钟后自动将日志级别变成ERROR -->
    <appender name="CONSOLE_PROD" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
        <filter class="com.zte.itp.msa.logback.TimedThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <!-- 此appender会往日志文件输出，会限制容量，不适合flume采集。 -->
    <appender name="isoaapp" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${spring.application.name}_app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${spring.application.name}-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>5</maxHistory>
            <totalSizeCap>50MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 此appender会将日志转为大数据应用日志标准格式，并推到KAFKA，项目组直接使用 log.info(message) 即可。 -->
    <appender name="isoakafka" class="com.github.danielwegener.logback.kafka.KafkaAppender">
        <topic>${isoalog.kafka.topic}</topic>
        <keyingStrategy class="com.github.danielwegener.logback.kafka.keying.LoggerNameKeyingStrategy"/>
        <!-- 采用异步线程推KAFKA，防止因为网络等不确定因素对业务系统产生影响。 -->
        <deliveryStrategy class="com.zte.itp.msa.logback.KafkaLogDileveryStrategy"/>
        <!-- 注意kafka的地址不能配错，默认用到的域名itnode01.bigdata、itnode02.bigdata、itnode03.bigdata  需要在本地hosts配置IP映射。 -->
        <!-- IP地址映射参看 https://wiki.zte.com.cn/pages/viewpage.action?pageId=379592215 中关于hosts的说明。 -->
        <producerConfig>
            bootstrap.servers=*************:9092,*************:9092,*************:9092
        </producerConfig>
        <producerConfig>
            metadata.fetch.timeout.ms=10000
        </producerConfig>
        <encoder class="com.zte.itp.msa.logback.KafkaLogEncoder"/>
    </appender>
    <!-- 测试环境 -->
    <appender name="isoakafka_test" class="com.github.danielwegener.logback.kafka.KafkaAppender">
        <topic>${isoalog.kafka.topic}</topic>
        <keyingStrategy class="com.github.danielwegener.logback.kafka.keying.LoggerNameKeyingStrategy"/>
        <!-- 采用异步线程推KAFKA，防止因为网络等不确定因素对业务系统产生影响。 -->
        <deliveryStrategy class="com.zte.itp.msa.logback.KafkaLogDileveryStrategy"/>
        <!-- 下面的服务器host(nodexxx.bigdata.com)是不存在的，需要自己在本地hosts作好映射IP关系，可查看 后面关于 hosts 的说明，这里不能直接使用IP。 -->
        <producerConfig>
            bootstrap.servers=************:9092,************:9092,************:9092
        </producerConfig>
        <producerConfig>
            metadata.fetch.timeout.ms=10000
        </producerConfig>
        <encoder class="com.zte.itp.msa.logback.KafkaLogEncoder"/>
        <appender-ref ref="isoaapp"/>
    </appender>

    <!-- 本地环境 -->
    <springProfile name="loc">
        <logger name="druid.sql.Statement" level="DEBUG"/>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="isoaapp"/>
        </root>
    </springProfile>
    <!-- 开发环境 -->
    <springProfile name="dev">
        <logger name="druid.sql.Statement" level="INFO"/>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="isoaapp"/>
            <appender-ref ref="isoakafka_test"/>
        </root>
    </springProfile>
    <!-- 测试环境 -->
    <springProfile name="test">
        <logger name="druid.sql.Statement" level="INFO"/>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="isoaapp"/>
            <appender-ref ref="isoakafka_test"/>
        </root>
    </springProfile>
    <!-- 仿真环境 -->
    <springProfile name="uat">
        <logger name="druid.sql.Statement" level="INFO"/>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="isoaapp"/>
        </root>
    </springProfile>
    <!-- 生产环境 -->
    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="CONSOLE_PROD"/>
            <appender-ref ref="isoaapp"/>
            <appender-ref ref="isoakafka"/>
        </root>
    </springProfile>
</configuration> 