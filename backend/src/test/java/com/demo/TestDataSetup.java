package com.demo;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

/**
 * 测试数据设置配置类
 * 为测试环境提供测试数据和配置
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@TestConfiguration
@Profile("test")
public class TestDataSetup {

    /**
     * 创建测试用的数据源配置
     * 使用H2内存数据库进行测试
     */
    @Bean
    @Primary
    public String testDataSourceInfo() {
        return "H2 Test Database Configuration";
    }
    
    /**
     * 测试环境特殊配置标识
     */
    @Bean
    public String testEnvironmentMarker() {
        return "TEST_ENVIRONMENT_ACTIVE";
    }
}
