package com.demo.controller;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 简单的Controller测试类
 * 用于验证测试环境配置是否正确
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@DisplayName("简单Controller测试")
class SimpleControllerTest extends BaseControllerTest {

    @Test
    @DisplayName("测试分类列表查询 - 基础连通性测试")
    void testCategoryListBasicConnectivity() throws Exception {
        // 简单的连通性测试，验证MockMvc是否正常工作
        mockMvc.perform(get("/api/categories")
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
