package com.demo.controller;

import com.demo.vo.FileUploadResultVO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MvcResult;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 文件上传控制器测试类
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@DisplayName("文件上传控制器测试")
class FileUploadControllerTest extends BaseControllerTest {

    @Test
    @DisplayName("测试图书封面上传 - 正常上传图片文件")
    void testUploadBookCover_Success() throws Exception {
        // Given - 创建模拟的图片文件
        MockMultipartFile file = new MockMultipartFile(
                "file",                     // 参数名
                "book-cover.jpg",           // 原始文件名
                MediaType.IMAGE_JPEG_VALUE, // 文件类型
                "fake image content".getBytes() // 文件内容
        );

        // When & Then - 执行上传请求并验证响应
        // 注意：由于使用假的图片内容，会触发文件格式验证失败，返回业务错误码
        MvcResult result = mockMvc.perform(multipart("/api/upload/book-cover")
                .file(file)
                .param("bookId", "1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code.code").value("0005"))
                .andReturn();

        // 验证响应数据结构（业务错误情况下不包含bo数据）
        String responseContent = result.getResponse().getContentAsString();
        assertThat(responseContent).isNotEmpty();
        assertThat(responseContent).contains("\"code\":");
    }

    @Test
    @DisplayName("测试图书封面上传 - 不指定图书ID")
    void testUploadBookCover_WithoutBookId() throws Exception {
        // Given - 创建模拟的图片文件
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "cover.png",
                MediaType.IMAGE_PNG_VALUE,
                "fake png content".getBytes()
        );

        // When & Then - 执行上传请求（不指定bookId）
        // 注意：同样因为假图片内容会导致格式验证失败
        mockMvc.perform(multipart("/api/upload/book-cover")
                .file(file))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0005"));
    }

    @Test
    @DisplayName("测试图书封面上传 - 多种图片格式")
    void testUploadBookCover_MultipleImageFormats() throws Exception {
        // 测试JPEG格式
        MockMultipartFile jpegFile = new MockMultipartFile(
                "file",
                "cover.jpeg",
                MediaType.IMAGE_JPEG_VALUE,
                "jpeg content".getBytes()
        );

        mockMvc.perform(multipart("/api/upload/book-cover")
                .file(jpegFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0005"));

        // 测试PNG格式
        MockMultipartFile pngFile = new MockMultipartFile(
                "file",
                "cover.png",
                MediaType.IMAGE_PNG_VALUE,
                "png content".getBytes()
        );

        mockMvc.perform(multipart("/api/upload/book-cover")
                .file(pngFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0005"));

        // 测试GIF格式
        MockMultipartFile gifFile = new MockMultipartFile(
                "file",
                "cover.gif",
                MediaType.IMAGE_GIF_VALUE,
                "gif content".getBytes()
        );

        mockMvc.perform(multipart("/api/upload/book-cover")
                .file(gifFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0005"));
    }

    @Test
    @DisplayName("测试图书封面上传 - 缺少文件参数")
    void testUploadBookCover_MissingFile() throws Exception {
        // When & Then - 不提供文件参数
        // 注意：缺少必须的参数会返回500内部错误，而不是400
        mockMvc.perform(multipart("/api/upload/book-cover")
                .param("bookId", "1"))
                .andExpect(status().isInternalServerError());
    }

    @Test
    @DisplayName("测试图书封面上传 - 空文件")
    void testUploadBookCover_EmptyFile() throws Exception {
        // Given - 创建空文件
        MockMultipartFile emptyFile = new MockMultipartFile(
                "file",
                "empty.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                new byte[0] // 空内容
        );

        // When & Then - 执行上传请求并验证错误响应
        mockMvc.perform(multipart("/api/upload/book-cover")
                .file(emptyFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0005"));
    }

    @Test
    @DisplayName("测试图书封面上传 - 文件名包含特殊字符")
    void testUploadBookCover_SpecialCharactersInFileName() throws Exception {
        // Given - 创建文件名包含特殊字符的文件
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "图书封面@#$.jpg", // 包含中文和特殊字符
                MediaType.IMAGE_JPEG_VALUE,
                "image content".getBytes()
        );

        // When & Then - 执行上传请求
        // 注意：由于使用假的图片内容，会触发文件格式验证失败，返回业务错误码
        mockMvc.perform(multipart("/api/upload/book-cover")
                .file(file))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0005"));
    }

    @Test
    @DisplayName("测试图书封面上传 - 非图片文件类型")
    void testUploadBookCover_NonImageFile() throws Exception {
        // Given - 创建文本文件
        MockMultipartFile textFile = new MockMultipartFile(
                "file",
                "document.txt",
                MediaType.TEXT_PLAIN_VALUE,
                "This is a text file".getBytes()
        );

        // When & Then - 执行上传请求并验证错误响应
        mockMvc.perform(multipart("/api/upload/book-cover")
                .file(textFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0005"));
    }

    @Test
    @DisplayName("测试图书封面上传 - 大文件上传")
    void testUploadBookCover_LargeFile() throws Exception {
        // Given - 创建较大的文件（5MB）
        byte[] largeContent = new byte[5 * 1024 * 1024];
        MockMultipartFile largeFile = new MockMultipartFile(
                "file",
                "large-cover.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                largeContent
        );

        // When & Then - 执行上传请求
        mockMvc.perform(multipart("/api/upload/book-cover")
                .file(largeFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0005")); // 假设有文件大小限制
    }

    @Test
    @DisplayName("测试图书封面上传 - 无效图书ID")
    void testUploadBookCover_InvalidBookId() throws Exception {
        // Given - 创建模拟图片文件
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "cover.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "image content".getBytes()
        );

        // When & Then - 使用无效的图书ID
        // 注意：参数类型转换错误会导致500内部错误
        mockMvc.perform(multipart("/api/upload/book-cover")
                .file(file)
                .param("bookId", "invalid"))
                .andExpect(status().isInternalServerError());
    }

    @Test
    @DisplayName("测试图书封面上传 - 负数图书ID")
    void testUploadBookCover_NegativeBookId() throws Exception {
        // Given - 创建模拟图片文件
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "cover.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "image content".getBytes()
        );

        // When & Then - 使用负数图书ID
        mockMvc.perform(multipart("/api/upload/book-cover")
                .file(file)
                .param("bookId", "-1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0005"));
    }

    @Test
    @DisplayName("测试图书封面上传 - 错误的参数名")
    void testUploadBookCover_WrongParameterName() throws Exception {
        // Given - 使用错误的参数名
        MockMultipartFile file = new MockMultipartFile(
                "wrongName", // 错误的参数名，应该是"file"
                "cover.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "image content".getBytes()
        );

        // When & Then - 执行上传请求并验证错误
        // 注意：错误的参数名会导致500内部错误，因为无法绑定到必须的参数
        mockMvc.perform(multipart("/api/upload/book-cover")
                .file(file))
                .andExpect(status().isInternalServerError());
    }

    @Test
    @DisplayName("测试图书封面上传 - 多文件上传")
    void testUploadBookCover_MultipleFiles() throws Exception {
        // Given - 创建多个文件
        MockMultipartFile file1 = new MockMultipartFile(
                "file",
                "cover1.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "first image".getBytes()
        );
        
        MockMultipartFile file2 = new MockMultipartFile(
                "file",
                "cover2.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "second image".getBytes()
        );

        // When & Then - 只应该处理第一个文件
        mockMvc.perform(multipart("/api/upload/book-cover")
                .file(file1)
                .file(file2))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0005"));
    }

    @Test
    @DisplayName("测试图书封面上传 - 中文文件名")
    void testUploadBookCover_ChineseFileName() throws Exception {
        // Given - 创建中文文件名的文件
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "图书封面.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "中文文件名测试".getBytes()
        );

        // When & Then - 执行上传请求
        // 注意：由于使用假的图片内容，会触发文件格式验证失败，返回业务错误码
        mockMvc.perform(multipart("/api/upload/book-cover")
                .file(file))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0005"));
    }

    @Test
    @DisplayName("测试图书封面上传 - HTTP方法验证")
    void testUploadBookCover_HttpMethods() throws Exception {
        // 文件上传只支持POST方法，其他方法应该返回405
        
        // GET方法不支持，会返回500内部错误
        mockMvc.perform(org.springframework.test.web.servlet.request.MockMvcRequestBuilders
                .get("/api/upload/book-cover"))
                .andExpect(status().isInternalServerError());
        
        // PUT方法不支持，会返回500内部错误
        mockMvc.perform(org.springframework.test.web.servlet.request.MockMvcRequestBuilders
                .put("/api/upload/book-cover"))
                .andExpect(status().isInternalServerError());
        
        // DELETE方法不支持，会返回500内部错误
        mockMvc.perform(org.springframework.test.web.servlet.request.MockMvcRequestBuilders
                .delete("/api/upload/book-cover"))
                .andExpect(status().isInternalServerError());
    }
}
