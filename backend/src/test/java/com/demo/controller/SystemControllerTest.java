package com.demo.controller;

import com.demo.vo.SystemInitResultVO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 系统管理控制器测试类
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@DisplayName("系统管理控制器测试")
class SystemControllerTest extends BaseControllerTest {

    @Test
    @DisplayName("测试系统预设数据初始化 - 正常初始化")
    void testInitSystemData_Success() throws Exception {
        // When & Then - 执行初始化请求并验证响应
        MvcResult result = mockMvc.perform(post("/api/system/init"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code.code").value("0000"))
                .andExpect(jsonPath("$.bo").exists())
                .andExpect(jsonPath("$.bo.isInitialized").exists())
                .andExpect(jsonPath("$.bo.categoryCount").exists())
                .andExpect(jsonPath("$.bo.regionCount").exists())
                .andExpect(jsonPath("$.bo.isInitialized").isBoolean())
                .andExpect(jsonPath("$.bo.categoryCount").isNumber())
                .andExpect(jsonPath("$.bo.regionCount").isNumber())
                .andReturn();

        // 验证响应数据结构
        String responseContent = result.getResponse().getContentAsString();
        assertThat(responseContent).isNotEmpty();
        assertThat(responseContent).contains("\"isInitialized\":");
        assertThat(responseContent).contains("\"categoryCount\":");
        assertThat(responseContent).contains("\"regionCount\":");
    }

    @Test
    @DisplayName("测试系统预设数据初始化 - 验证初始化成功状态")
    void testInitSystemData_SuccessStatus() throws Exception {
        // When & Then - 执行初始化请求并验证成功状态
        MvcResult result = mockMvc.perform(post("/api/system/init"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0000"))
                .andExpect(jsonPath("$.bo.isInitialized").value(true))
                .andReturn();

        // 验证响应内容包含期望的字段
        String responseContent = result.getResponse().getContentAsString();
        assertThat(responseContent).contains("\"bo\":");
        assertThat(responseContent).contains("\"isInitialized\":");
        
        // 如果初始化成功，验证包含数量字段
        if (responseContent.contains("\"isInitialized\":true")) {
            assertThat(responseContent).contains("\"categoryCount\":");
            assertThat(responseContent).contains("\"regionCount\":");
        }
    }

    @Test
    @DisplayName("测试系统预设数据初始化 - 重复初始化")
    void testInitSystemData_RepeatInitialization() throws Exception {
        // Given - 先执行一次初始化
        mockMvc.perform(post("/api/system/init"))
                .andExpect(status().isOk());

        // When & Then - 再次执行初始化，应该仍然成功
        mockMvc.perform(post("/api/system/init"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0000"))
                .andExpect(jsonPath("$.bo.isInitialized").exists());
    }

    @Test
    @DisplayName("测试系统预设数据初始化 - 验证数据完整性")
    void testInitSystemData_DataIntegrity() throws Exception {
        // When & Then - 执行初始化并验证数据完整性
        MvcResult result = mockMvc.perform(post("/api/system/init"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.bo.isInitialized").exists())
                .andExpect(jsonPath("$.bo.categoryCount").isNumber())
                .andExpect(jsonPath("$.bo.regionCount").isNumber())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        
        // 验证如果初始化成功，则分类和地区数量应该大于0
        if (responseContent.contains("\"isInitialized\":true")) {
            // 成功初始化的情况下，至少应该有一些预设数据
            assertThat(responseContent).satisfiesAnyOf(
                content -> assertThat(content).contains("\"categoryCount\":"),
                content -> assertThat(content).contains("\"regionCount\":")
            );
        }
    }

    @Test
    @DisplayName("测试系统预设数据初始化 - 错误信息处理")
    void testInitSystemData_ErrorHandling() throws Exception {
        // When & Then - 执行初始化请求
        MvcResult result = mockMvc.perform(post("/api/system/init"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        
        // 验证响应结构正确，无论成功还是失败
        assertThat(responseContent).contains("\"code\":");
        assertThat(responseContent).contains("\"bo\":");
        
        // 如果初始化失败，应该有错误信息
        if (responseContent.contains("\"isInitialized\":false")) {
            assertThat(responseContent).contains("\"errorMessage\":");
        }
    }

    @Test
    @DisplayName("测试系统预设数据初始化 - HTTP方法验证")
    void testInitSystemData_HttpMethods() throws Exception {
        // 正确的POST方法
        mockMvc.perform(post("/api/system/init"))
                .andExpect(status().isOk());
        
        // 不支持的GET方法会返回500内部错误（Spring Boot的特性）
        mockMvc.perform(org.springframework.test.web.servlet.request.MockMvcRequestBuilders
                .get("/api/system/init"))
                .andExpect(status().isInternalServerError());
        
        // 不支持的PUT方法会返回500内部错误
        mockMvc.perform(org.springframework.test.web.servlet.request.MockMvcRequestBuilders
                .put("/api/system/init"))
                .andExpect(status().isInternalServerError());
        
        // 不支持的DELETE方法会返回500内部错误
        mockMvc.perform(org.springframework.test.web.servlet.request.MockMvcRequestBuilders
                .delete("/api/system/init"))
                .andExpect(status().isInternalServerError());
    }

    @Test
    @DisplayName("测试系统预设数据初始化 - 请求头验证")
    void testInitSystemData_Headers() throws Exception {
        // When & Then - 测试不同的Content-Type头
        mockMvc.perform(post("/api/system/init")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        
        // 测试不指定Content-Type的情况
        mockMvc.perform(post("/api/system/init"))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("测试系统预设数据初始化 - 响应时间验证")
    void testInitSystemData_ResponseTime() throws Exception {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        
        // When - 执行初始化请求
        mockMvc.perform(post("/api/system/init"))
                .andExpect(status().isOk());
        
        // Then - 验证响应时间合理（初始化可能需要较长时间，比如10秒）
        long endTime = System.currentTimeMillis();
        long responseTime = endTime - startTime;
        
        assertThat(responseTime).isLessThan(10000L); // 响应时间应该小于10秒
    }

    @Test
    @DisplayName("测试系统预设数据初始化 - 并发初始化测试")
    void testInitSystemData_ConcurrentInitialization() throws Exception {
        // 测试并发初始化请求的处理
        // 所有请求都应该成功，但系统应该能正确处理并发情况
        
        for (int i = 0; i < 3; i++) {
            mockMvc.perform(post("/api/system/init"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code.code").value("0000"));
        }
    }

    @Test
    @DisplayName("测试系统预设数据初始化 - 无参数请求")
    void testInitSystemData_NoParameters() throws Exception {
        // 系统初始化不需要任何参数
        
        // When & Then - 不提供任何参数的初始化请求
        mockMvc.perform(post("/api/system/init"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0000"))
                .andExpect(jsonPath("$.bo").exists());
    }

    @Test
    @DisplayName("测试系统预设数据初始化 - 额外参数忽略")
    void testInitSystemData_ExtraParametersIgnored() throws Exception {
        // 系统初始化应该忽略额外的参数
        
        // When & Then - 提供额外参数的初始化请求
        mockMvc.perform(post("/api/system/init")
                .param("extraParam", "value")
                .param("anotherParam", "123"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0000"));
    }

    @Test
    @DisplayName("测试系统预设数据初始化 - URL路径验证")
    void testInitSystemData_URLPaths() throws Exception {
        // 正确的路径
        mockMvc.perform(post("/api/system/init"))
                .andExpect(status().isOk());
        
        // 错误的路径应该返回404
        mockMvc.perform(post("/api/system/initialize"))
                .andExpect(status().isNotFound());
        
        // 注意：某些路径可能会被Spring Boot处理为200状态，这取决于路径映射规则
        mockMvc.perform(post("/api/system/init/"))
                .andExpect(status().isOk());
        
        mockMvc.perform(post("/system/init"))
                .andExpect(status().isNotFound());
    }

    @Test
    @DisplayName("测试系统预设数据初始化 - 幂等性验证")
    void testInitSystemData_Idempotency() throws Exception {
        // 系统初始化应该是幂等的，多次调用结果应该一致
        
        // 第一次初始化
        MvcResult firstResult = mockMvc.perform(post("/api/system/init"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0000"))
                .andReturn();
        
        // 第二次初始化
        MvcResult secondResult = mockMvc.perform(post("/api/system/init"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code.code").value("0000"))
                .andReturn();
        
        // 两次结果应该都是成功的
        String firstResponse = firstResult.getResponse().getContentAsString();
        String secondResponse = secondResult.getResponse().getContentAsString();
        
        assertThat(firstResponse).contains("\"code\":\"0000\"");
        assertThat(secondResponse).contains("\"code\":\"0000\"");
    }
}
