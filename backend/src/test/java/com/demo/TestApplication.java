package com.demo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 测试应用程序启动类
 * 专门用于测试环境，排除MSA相关配置
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@SpringBootApplication(exclude = {
    KafkaAutoConfiguration.class
}, scanBasePackages = {"com.demo"})
public class TestApplication {

    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }
}
