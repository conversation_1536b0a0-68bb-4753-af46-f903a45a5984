#!/bin/bash

echo "========================================"
echo "图书管理系统 Controller层单元测试"
echo "========================================"

echo ""
echo "1. 运行完整测试套件..."
mvn test -Dtest=ControllerTestSuite

echo ""
echo "2. 运行图书管理控制器测试..."
mvn test -Dtest=BookControllerTest

echo ""
echo "3. 运行分类管理控制器测试..."
mvn test -Dtest=CategoryControllerTest

echo ""
echo "4. 运行文件上传控制器测试..."
mvn test -Dtest=FileUploadControllerTest

echo ""
echo "5. 运行系统管理控制器测试..."
mvn test -Dtest=SystemControllerTest

echo ""
echo "========================================"
echo "测试完成！"
echo "========================================"
