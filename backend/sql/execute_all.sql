-- =============================================
-- 图书管理系统完整数据库初始化脚本
-- 创建时间: 2024-01-15
-- 描述: 一次性执行所有数据库初始化操作
-- 使用方法: mysql -u root -p < execute_all.sql
-- =============================================

-- =============================================
-- 1. 创建数据库
-- =============================================

-- 如果数据库已存在则删除
DROP DATABASE IF EXISTS liuzhiping;

-- 创建数据库
CREATE DATABASE liuzhiping 
    DEFAULT CHARACTER SET utf8mb4 
    DEFAULT COLLATE utf8mb4_general_ci
    COMMENT '图书管理系统数据库';

-- 使用数据库
USE liuzhiping;

-- 设置数据库编码
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

SELECT 'Database liuzhiping created successfully!' AS message;

-- =============================================
-- 2. 创建表结构
-- =============================================

-- 创建分类信息表 (categories)
CREATE TABLE categories (
    -- 基础字段（必须）
    oid BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', 
    last_updated_by VARCHAR(32) NOT NULL COMMENT '更新人',
    last_updated_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    tenant_id VARCHAR(64) NOT NULL DEFAULT '10001' COMMENT '租户ID',
    enable_flag CHAR(1) NOT NULL DEFAULT 'T' COMMENT '有效标识',
    
    -- 业务字段
    category_name VARCHAR(50) NOT NULL UNIQUE COMMENT '分类名称，系统内唯一',
    category_description VARCHAR(500) COMMENT '分类描述',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序号',
    category_status CHAR(1) NOT NULL DEFAULT 'T' COMMENT '分类状态',
    is_preset CHAR(1) NOT NULL DEFAULT 'F' COMMENT '是否预设分类',
    
    -- 索引设计
    INDEX idx_category_name (category_name),
    INDEX idx_sort_order (sort_order),
    INDEX idx_category_status (category_status),
    INDEX idx_is_preset (is_preset),
    INDEX idx_tenant_enable (tenant_id, enable_flag),
    
    -- 唯一约束
    UNIQUE KEY uk_category_name_tenant (category_name, tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类信息表';

-- 创建地区信息表 (regions)
CREATE TABLE regions (
    -- 基础字段（必须）
    oid BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    last_updated_by VARCHAR(32) NOT NULL COMMENT '更新人',
    last_updated_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', 
    tenant_id VARCHAR(64) NOT NULL DEFAULT '10001' COMMENT '租户ID',
    enable_flag CHAR(1) NOT NULL DEFAULT 'T' COMMENT '有效标识',
    
    -- 业务字段
    region_name VARCHAR(100) NOT NULL COMMENT '地区名称，系统内唯一',
    region_address VARCHAR(200) NOT NULL COMMENT '地区地址',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    region_description VARCHAR(500) COMMENT '地区描述',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序号',
    region_status CHAR(1) NOT NULL DEFAULT 'T' COMMENT '地区状态',
    is_preset CHAR(1) NOT NULL DEFAULT 'F' COMMENT '是否预设地区',
    
    -- 索引设计
    INDEX idx_region_name (region_name),
    INDEX idx_sort_order (sort_order),
    INDEX idx_region_status (region_status),
    INDEX idx_is_preset (is_preset),
    INDEX idx_tenant_enable (tenant_id, enable_flag),
    
    -- 唯一约束
    UNIQUE KEY uk_region_name_tenant (region_name, tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区信息表';

-- 创建图书信息表 (books)
CREATE TABLE books (
    -- 基础字段（必须）
    oid BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    last_updated_by VARCHAR(32) NOT NULL COMMENT '更新人', 
    last_updated_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    tenant_id VARCHAR(64) NOT NULL DEFAULT '10001' COMMENT '租户ID',
    enable_flag CHAR(1) NOT NULL DEFAULT 'T' COMMENT '有效标识',
    
    -- 业务字段
    book_title VARCHAR(200) NOT NULL COMMENT '图书标题',
    book_author VARCHAR(300) COMMENT '图书作者，多个作者用逗号分隔',
    isbn VARCHAR(20) NOT NULL UNIQUE COMMENT 'ISBN编号，全局唯一',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    region_id BIGINT NOT NULL COMMENT '地区ID', 
    total_quantity INT NOT NULL DEFAULT 1 COMMENT '总册数',
    available_quantity INT NOT NULL DEFAULT 1 COMMENT '可借册数',
    borrowed_quantity INT NOT NULL DEFAULT 0 COMMENT '已借册数',
    publication_year YEAR COMMENT '出版年份',
    book_description TEXT COMMENT '图书描述',
    cover_image_url VARCHAR(500) COMMENT '封面图片URL',
    book_status VARCHAR(32) NOT NULL DEFAULT 'AVAILABLE' COMMENT '图书状态',
    
    -- 索引设计
    INDEX idx_book_title (book_title),
    INDEX idx_book_author (book_author),  
    INDEX idx_isbn (isbn),
    INDEX idx_category_id (category_id),
    INDEX idx_region_id (region_id),
    INDEX idx_created_date (created_date),
    INDEX idx_book_status (book_status),
    INDEX idx_tenant_enable (tenant_id, enable_flag),
    
    -- 外键约束
    FOREIGN KEY (category_id) REFERENCES categories(oid),
    FOREIGN KEY (region_id) REFERENCES regions(oid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书信息表';

SELECT 'All tables created successfully!' AS message;

-- =============================================
-- 3. 插入预设数据
-- =============================================

-- 插入8个预设图书分类
INSERT INTO categories (category_name, category_description, sort_order, is_preset, created_by, last_updated_by) VALUES
('文学艺术', '文学、艺术、美学类图书', 1, 'T', 'system', 'system'),
('科学技术', '科学、技术、工程类图书', 2, 'T', 'system', 'system'),
('社会科学', '社会科学、人文科学类图书', 3, 'T', 'system', 'system'),
('经济管理', '经济学、管理学类图书', 4, 'T', 'system', 'system'),
('历史地理', '历史、地理、传记类图书', 5, 'T', 'system', 'system'),
('医学健康', '医学、健康、养生类图书', 6, 'T', 'system', 'system'),
('教育心理', '教育学、心理学类图书', 7, 'T', 'system', 'system'),
('工具参考', '词典、手册、参考书类', 8, 'T', 'system', 'system');

-- 插入3个预设地区
INSERT INTO regions (region_name, region_address, contact_phone, region_description, sort_order, is_preset, created_by, last_updated_by) VALUES
('总馆', '北京市海淀区中关村南大街1号', '010-12345678', '图书管理系统总馆，负责全系统的统一管理', 1, 'T', 'system', 'system'),
('上海分馆', '上海市浦东新区张江高科技园区', '021-87654321', '上海地区分馆，服务上海及华东地区', 2, 'T', 'system', 'system'),
('深圳分馆', '深圳市南山区高新技术产业园区', '0755-98765432', '深圳地区分馆，服务深圳及华南地区', 3, 'T', 'system', 'system');

SELECT 'Preset data inserted successfully!' AS message;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 4. 显示初始化完成信息
-- =============================================

SELECT '=== 数据库初始化完成 ===' AS info;

-- 显示表信息
SHOW TABLES;

-- 验证预设数据
SELECT CONCAT('预设数据初始化完成! 分类: ', 
    (SELECT COUNT(*) FROM categories WHERE is_preset = 'T'), 
    '个, 地区: ', 
    (SELECT COUNT(*) FROM regions WHERE is_preset = 'T'), 
    '个') AS summary;

SELECT 'Database initialization completed successfully!' AS final_message;
SELECT 'You can now run 04_test_data.sql to insert sample books data.' AS next_step;
