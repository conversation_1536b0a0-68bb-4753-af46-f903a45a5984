-- =============================================
-- 图书管理系统初始化数据脚本
-- 创建时间: 2024-01-15
-- 描述: 插入系统预设数据（分类和地区）
-- 注意: 严格按照详细设计文档的预设数据创建
-- =============================================

USE liuzhiping;

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 插入预设分类数据
-- 说明: 插入8个系统预设图书分类
-- =============================================

-- 清空分类表数据（如果需要重新初始化）
-- DELETE FROM categories WHERE is_preset = 'T';

-- 插入8个预设图书分类
INSERT INTO categories (category_name, category_description, sort_order, category_status, is_preset, created_by, last_updated_by, tenant_id, enable_flag) VALUES
('文学艺术', '文学、艺术、美学类图书', 1, 'T', 'T', 'system', 'system', '10001', 'T'),
('科学技术', '科学、技术、工程类图书', 2, 'T', 'T', 'system', 'system', '10001', 'T'),
('社会科学', '社会科学、人文科学类图书', 3, 'T', 'T', 'system', 'system', '10001', 'T'),
('经济管理', '经济学、管理学类图书', 4, 'T', 'T', 'system', 'system', '10001', 'T'),
('历史地理', '历史、地理、传记类图书', 5, 'T', 'T', 'system', 'system', '10001', 'T'),
('医学健康', '医学、健康、养生类图书', 6, 'T', 'T', 'system', 'system', '10001', 'T'),
('教育心理', '教育学、心理学类图书', 7, 'T', 'T', 'system', 'system', '10001', 'T'),
('工具参考', '词典、手册、参考书类', 8, 'T', 'T', 'system', 'system', '10001', 'T');

-- =============================================
-- 2. 插入预设地区数据
-- 说明: 插入3个系统预设地区
-- =============================================

-- 清空地区表数据（如果需要重新初始化）
-- DELETE FROM regions WHERE is_preset = 'T';

-- 插入3个预设地区
INSERT INTO regions (region_name, region_address, contact_phone, region_description, sort_order, region_status, is_preset, created_by, last_updated_by, tenant_id, enable_flag) VALUES
('总馆', '北京市海淀区中关村南大街1号', '010-12345678', '图书管理系统总馆，负责全系统的统一管理', 1, 'T', 'T', 'system', 'system', '10001', 'T'),
('上海分馆', '上海市浦东新区张江高科技园区', '021-87654321', '上海地区分馆，服务上海及华东地区', 2, 'T', 'T', 'system', 'system', '10001', 'T'),
('深圳分馆', '深圳市南山区高新技术产业园区', '0755-98765432', '深圳地区分馆，服务深圳及华南地区', 3, 'T', 'T', 'system', 'system', '10001', 'T');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 3. 验证初始化数据
-- =============================================

-- 验证分类数据
SELECT '=== 分类数据验证 ===' AS info;
SELECT oid, category_name, category_description, sort_order, is_preset, category_status, enable_flag 
FROM categories 
WHERE is_preset = 'T' 
ORDER BY sort_order;

-- 验证地区数据
SELECT '=== 地区数据验证 ===' AS info;
SELECT oid, region_name, region_address, contact_phone, sort_order, is_preset, region_status, enable_flag 
FROM regions 
WHERE is_preset = 'T' 
ORDER BY sort_order;

-- 显示初始化完成信息
SELECT CONCAT('预设数据初始化完成! 分类: ', 
    (SELECT COUNT(*) FROM categories WHERE is_preset = 'T'), 
    '个, 地区: ', 
    (SELECT COUNT(*) FROM regions WHERE is_preset = 'T'), 
    '个') AS message;
