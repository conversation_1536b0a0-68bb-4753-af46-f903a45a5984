# 图书管理系统数据库脚本说明

## 脚本执行顺序

请按照以下顺序执行SQL脚本，确保数据库正确创建和初始化：

### 1. 创建数据库
```bash
mysql -u root -p < 01_create_database.sql
```
- 创建数据库：`liuzhiping`
- 设置字符集：`utf8mb4`
- 设置排序规则：`utf8mb4_general_ci`

### 2. 创建表结构
```bash
mysql -u root -p < 02_create_tables.sql
```
- 创建3张核心表：`categories`、`regions`、`books`
- 严格按照详细设计文档的DDL语句创建
- 包含完整的索引和外键约束

### 3. 初始化预设数据
```bash
mysql -u root -p < 03_init_data.sql
```
- 插入8个预设图书分类
- 插入3个预设地区信息
- 系统必需的基础数据

### 4. 插入测试数据
```bash
mysql -u root -p < 04_test_data.sql
```
- 插入真实的图书测试数据
- 覆盖所有分类和地区
- 包含各种状态的测试场景

## 一键执行脚本

也可以使用以下命令一次性执行所有脚本：

```bash
# Linux/Mac
cat 01_create_database.sql 02_create_tables.sql 03_init_data.sql 04_test_data.sql | mysql -u root -p

# Windows
type 01_create_database.sql 02_create_tables.sql 03_init_data.sql 04_test_data.sql | mysql -u root -p
```

## 数据库连接信息

- **数据库名**: `liuzhiping`
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_general_ci`
- **默认端口**: `3306`

## 表结构说明

### 1. categories（分类信息表）
- **主要字段**: 分类名称、分类描述、排序号、状态等
- **预设数据**: 8个系统预设分类
- **业务规则**: 分类名称在租户内唯一

### 2. regions（地区信息表）
- **主要字段**: 地区名称、地区地址、联系电话、状态等
- **预设数据**: 3个系统预设地区
- **业务规则**: 地区名称在租户内唯一

### 3. books（图书信息表）
- **主要字段**: 图书标题、作者、ISBN、分类、地区、库存等
- **外键关联**: 关联categories和regions表
- **业务规则**: ISBN全局唯一，库存数量逻辑一致

## 测试数据说明

### 数据分布
- **文学艺术类**: 7本（包含古典文学、现代文学、外国文学）
- **科学技术类**: 9本（Java、Python、数据库、算法等）
- **社会科学类**: 3本（心理学、社会学等）
- **经济管理类**: 5本（管理学、经济学、创业投资）
- **历史地理类**: 3本（中国历史、世界历史）
- **医学健康类**: 2本（中医经典、现代医学）
- **教育心理类**: 2本（教育学、阅读方法）
- **工具参考类**: 3本（词典、手册等）

### 状态覆盖
- **AVAILABLE**: 可借阅（大部分图书）
- **DAMAGED**: 已损坏（1本）
- **LOST**: 已遗失（1本）
- **UNAVAILABLE**: 不可借阅（1本内部资料）

### 地区分布
- **总馆**: 主要收藏，涵盖各类图书
- **上海分馆**: 技术类和管理类图书较多
- **深圳分馆**: 经济类和工具类图书较多

## 数据验证

执行脚本后，可通过以下SQL验证数据：

```sql
-- 查看表创建情况
SHOW TABLES;

-- 查看分类统计
SELECT c.category_name, COUNT(b.oid) as book_count 
FROM categories c 
LEFT JOIN books b ON c.oid = b.category_id 
GROUP BY c.oid;

-- 查看地区统计
SELECT r.region_name, COUNT(b.oid) as book_count 
FROM regions r 
LEFT JOIN books b ON r.oid = b.region_id 
GROUP BY r.oid;

-- 查看状态统计
SELECT book_status, COUNT(*) as count 
FROM books 
GROUP BY book_status;
```

## 注意事项

1. **字符编码**: 确保MySQL客户端使用UTF-8编码
2. **权限设置**: 执行用户需要有创建数据库和表的权限
3. **外键约束**: 严格按照顺序执行，避免外键约束错误
4. **数据完整性**: 测试数据符合所有业务规则和约束条件
5. **真实性**: 所有图书信息均来自真实出版物，ISBN编号真实有效

## 故障排除

### 常见问题
1. **字符编码问题**: 设置MySQL客户端字符集为utf8mb4
2. **外键约束错误**: 检查表创建顺序，确保父表先于子表创建
3. **唯一约束冲突**: 检查是否重复执行了初始化脚本
4. **权限不足**: 确保数据库用户有足够的权限

### 重新初始化
如需重新初始化数据库，请执行：
```sql
DROP DATABASE IF EXISTS liuzhiping;
```
然后重新按顺序执行所有脚本。
