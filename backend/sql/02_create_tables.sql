-- =============================================
-- 图书管理系统表结构创建脚本
-- 创建时间: 2024-01-15
-- 描述: 创建图书管理系统所有表结构
-- 注意: 严格按照详细设计文档创建，遵循外键依赖关系
-- =============================================

USE liuzhiping;

-- 设置外键检查
-- SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 创建分类信息表 (categories)
-- 说明: 基础表，无外键依赖，需要首先创建
-- =============================================

DROP TABLE IF EXISTS categories;

CREATE TABLE categories (
    -- 基础字段（必须）
    oid BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', 
    last_updated_by VARCHAR(32) NOT NULL COMMENT '更新人',
    last_updated_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    tenant_id VARCHAR(64) NOT NULL DEFAULT '10001' COMMENT '租户ID',
    enable_flag CHAR(1) NOT NULL DEFAULT 'T' COMMENT '有效标识',
    
    -- 业务字段
    category_name VARCHAR(50) NOT NULL UNIQUE COMMENT '分类名称，系统内唯一',
    category_description VARCHAR(500) COMMENT '分类描述',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序号',
    category_status CHAR(1) NOT NULL DEFAULT 'T' COMMENT '分类状态',
    is_preset CHAR(1) NOT NULL DEFAULT 'F' COMMENT '是否预设分类',
    
    -- 索引设计
    INDEX idx_category_name (category_name),
    INDEX idx_sort_order (sort_order),
    INDEX idx_category_status (category_status),
    INDEX idx_is_preset (is_preset),
    INDEX idx_tenant_enable (tenant_id, enable_flag),
    
    -- 唯一约束
    UNIQUE KEY uk_category_name_tenant (category_name, tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类信息表';

-- =============================================
-- 2. 创建地区信息表 (regions)
-- 说明: 基础表，无外键依赖，需要首先创建
-- =============================================

DROP TABLE IF EXISTS regions;

CREATE TABLE regions (
    -- 基础字段（必须）
    oid BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    last_updated_by VARCHAR(32) NOT NULL COMMENT '更新人',
    last_updated_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', 
    tenant_id VARCHAR(64) NOT NULL DEFAULT '10001' COMMENT '租户ID',
    enable_flag CHAR(1) NOT NULL DEFAULT 'T' COMMENT '有效标识',
    
    -- 业务字段
    region_name VARCHAR(100) NOT NULL COMMENT '地区名称，系统内唯一',
    region_address VARCHAR(200) NOT NULL COMMENT '地区地址',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    region_description VARCHAR(500) COMMENT '地区描述',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序号',
    region_status CHAR(1) NOT NULL DEFAULT 'T' COMMENT '地区状态',
    is_preset CHAR(1) NOT NULL DEFAULT 'F' COMMENT '是否预设地区',
    
    -- 索引设计
    INDEX idx_region_name (region_name),
    INDEX idx_sort_order (sort_order),
    INDEX idx_region_status (region_status),
    INDEX idx_is_preset (is_preset),
    INDEX idx_tenant_enable (tenant_id, enable_flag),
    
    -- 唯一约束
    UNIQUE KEY uk_region_name_tenant (region_name, tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区信息表';

-- =============================================
-- 3. 创建图书信息表 (books)
-- 说明: 业务核心表，依赖categories和regions表
-- =============================================

DROP TABLE IF EXISTS books;

CREATE TABLE books (
    oid BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    created_by VARCHAR(32) NOT NULL COMMENT '创建人',
    created_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    last_updated_by VARCHAR(32) NOT NULL COMMENT '更新人', 
    last_updated_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    tenant_id VARCHAR(64) NOT NULL DEFAULT '10001' COMMENT '租户ID',
    enable_flag CHAR(1) NOT NULL DEFAULT 'T' COMMENT '有效标识',
    
    -- 业务字段
    book_title VARCHAR(200) NOT NULL COMMENT '图书标题',
    book_author VARCHAR(300) COMMENT '图书作者，多个作者用逗号分隔',
    isbn VARCHAR(20) NOT NULL UNIQUE COMMENT 'ISBN编号，全局唯一',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    region_id BIGINT NOT NULL COMMENT '地区ID', 
    total_quantity INT NOT NULL DEFAULT 1 COMMENT '总册数',
    available_quantity INT NOT NULL DEFAULT 1 COMMENT '可借册数',
    borrowed_quantity INT NOT NULL DEFAULT 0 COMMENT '已借册数',
    publication_year YEAR COMMENT '出版年份',
    book_description TEXT COMMENT '图书描述',
    cover_image_url VARCHAR(500) COMMENT '封面图片URL',
    book_status VARCHAR(32) NOT NULL DEFAULT 'AVAILABLE' COMMENT '图书状态',
    
    -- 索引设计
    INDEX idx_book_title (book_title),
    INDEX idx_book_author (book_author),  
    INDEX idx_isbn (isbn),
    INDEX idx_category_id (category_id),
    INDEX idx_region_id (region_id),
    INDEX idx_created_date (created_date),
    INDEX idx_book_status (book_status),
    INDEX idx_tenant_enable (tenant_id, enable_flag),
    
    -- 外键约束
    FOREIGN KEY (category_id) REFERENCES categories(oid),
    FOREIGN KEY (region_id) REFERENCES regions(oid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书信息表';

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示创建结果
SELECT 'All tables created successfully!' AS message;

-- 显示表结构信息
SHOW TABLES;
