-- =============================================
-- 图书管理系统测试数据脚本
-- 创建时间: 2024-01-15
-- 描述: 插入真实的测试数据，覆盖各种业务场景
-- 注意: 数据尽可能真实，参考实际图书信息
-- =============================================

USE liuzhiping;

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 插入科学技术类图书测试数据
-- =============================================

INSERT INTO books (book_title, book_author, isbn, category_id, region_id, total_quantity, available_quantity, borrowed_quantity, publication_year, book_description, cover_image_url, book_status, created_by, last_updated_by) VALUES
-- Java相关技术书籍
('Java编程思想', 'Bruce Eckel', '9787111213826', 2, 1, 5, 3, 2, 2007, 'Java编程经典教材，全面深入地介绍了Java语言的各个方面，适合初学者和有经验的程序员。', '/images/books/java-thinking.jpg', 'AVAILABLE', 'admin', 'admin'),
('Effective Java', 'Joshua Bloch', '9787121123726', 2, 1, 3, 1, 2, 2018, 'Java之父Joshua Bloch的经典著作，提供了78条Java编程的最佳实践。', '/images/books/effective-java.jpg', 'AVAILABLE', 'admin', 'admin'),
('Spring实战', 'Craig Walls', '9787115391681', 2, 2, 4, 4, 0, 2020, 'Spring框架权威指南，深入讲解Spring核心特性和最佳实践。', '/images/books/spring-in-action.jpg', 'AVAILABLE', 'admin', 'admin'),

-- Python相关技术书籍  
('Python编程：从入门到实践', 'Eric Matthes', '9787115428028', 2, 1, 6, 4, 2, 2019, 'Python入门经典教材，通过项目实践学习Python编程。', '/images/books/python-crash-course.jpg', 'AVAILABLE', 'admin', 'admin'),
('流畅的Python', 'Luciano Ramalho', '9787115454411', 2, 2, 3, 2, 1, 2017, '深入理解Python语言特性，掌握Pythonic编程风格。', '/images/books/fluent-python.jpg', 'AVAILABLE', 'admin', 'admin'),

-- 数据库相关书籍
('MySQL必知必会', 'Ben Forta', '9787115187048', 2, 1, 8, 6, 2, 2009, 'MySQL数据库经典入门教材，简洁实用的SQL学习指南。', '/images/books/mysql-crash-course.jpg', 'AVAILABLE', 'admin', 'admin'),
('高性能MySQL', 'Baron Schwartz,Peter Zaitsev', '9787121198854', 2, 3, 2, 1, 1, 2013, 'MySQL性能优化权威指南，深入讲解MySQL架构和调优技术。', '/images/books/high-performance-mysql.jpg', 'AVAILABLE', 'admin', 'admin'),

-- 算法和数据结构
('算法导论', 'Thomas H.Cormen,Charles E.Leiserson', '9787111407010', 2, 1, 4, 3, 1, 2012, '计算机科学算法经典教材，全面系统地介绍了算法设计与分析。', '/images/books/introduction-to-algorithms.jpg', 'AVAILABLE', 'admin', 'admin'),
('剑指Offer', '何海涛', '9787121275456', 2, 2, 5, 3, 2, 2017, '面试算法题经典书籍，帮助程序员提升算法思维和编程能力。', '/images/books/coding-interviews.jpg', 'AVAILABLE', 'admin', 'admin');

-- =============================================
-- 2. 插入文学艺术类图书测试数据
-- =============================================

INSERT INTO books (book_title, book_author, isbn, category_id, region_id, total_quantity, available_quantity, borrowed_quantity, publication_year, book_description, cover_image_url, book_status, created_by, last_updated_by) VALUES
-- 中国古典文学
('红楼梦', '曹雪芹', '9787020002207', 1, 1, 10, 7, 3, 2008, '中国古典文学四大名著之一，描写贾、史、王、薛四大家族的兴衰历程。', '/images/books/dream-of-red-chamber.jpg', 'AVAILABLE', 'admin', 'admin'),
('三国演义', '罗贯中', '9787020002214', 1, 2, 8, 5, 3, 2008, '中国古典历史小说的巅峰之作，描写三国时期的政治军事斗争。', '/images/books/romance-of-three-kingdoms.jpg', 'AVAILABLE', 'admin', 'admin'),
('西游记', '吴承恩', '9787020002221', 1, 3, 6, 4, 2, 2008, '中国古典神话小说，讲述孙悟空保护唐僧西天取经的故事。', '/images/books/journey-to-the-west.jpg', 'AVAILABLE', 'admin', 'admin'),

-- 现代文学
('活着', '余华', '9787506316972', 1, 1, 7, 4, 3, 2012, '当代中国文学经典作品，描写一个人和他命运之间的友情。', '/images/books/to-live.jpg', 'AVAILABLE', 'admin', 'admin'),
('平凡的世界', '路遥', '9787020035694', 1, 2, 9, 6, 3, 2012, '茅盾文学奖获奖作品，展现中国西北农村的生活变迁。', '/images/books/ordinary-world.jpg', 'AVAILABLE', 'admin', 'admin'),

-- 外国文学
('百年孤独', '加西亚·马尔克斯', '9787544729765', 1, 1, 4, 2, 2, 2011, '魔幻现实主义文学的代表作，讲述布恩迪亚家族七代人的传奇故事。', '/images/books/one-hundred-years-of-solitude.jpg', 'AVAILABLE', 'admin', 'admin'),
('1984', '乔治·奥威尔', '9787544729222', 1, 3, 5, 3, 2, 2010, '反乌托邦小说经典，描绘了一个极权主义社会的恐怖图景。', '/images/books/1984.jpg', 'AVAILABLE', 'admin', 'admin');

-- =============================================
-- 3. 插入经济管理类图书测试数据
-- =============================================

INSERT INTO books (book_title, book_author, isbn, category_id, region_id, total_quantity, available_quantity, borrowed_quantity, publication_year, book_description, cover_image_url, book_status, created_by, last_updated_by) VALUES
-- 管理学经典
('管理学原理', '史蒂芬·罗宾斯', '9787300174396', 4, 1, 6, 4, 2, 2014, '管理学经典教材，全面系统地介绍了管理学的基本理论和实践。', '/images/books/principles-of-management.jpg', 'AVAILABLE', 'admin', 'admin'),
('从优秀到卓越', '吉姆·柯林斯', '9787508613604', 4, 2, 4, 3, 1, 2009, '企业管理经典著作，揭示了优秀公司成为卓越公司的秘密。', '/images/books/good-to-great.jpg', 'AVAILABLE', 'admin', 'admin'),

-- 经济学
('经济学原理', '格里高利·曼昆', '9787301168004', 4, 1, 5, 3, 2, 2012, '经济学入门经典教材，深入浅出地介绍了微观和宏观经济学原理。', '/images/books/principles-of-economics.jpg', 'AVAILABLE', 'admin', 'admin'),
('国富论', '亚当·斯密', '9787100040655', 4, 3, 3, 2, 1, 2011, '经济学开山之作，奠定了现代经济学的理论基础。', '/images/books/wealth-of-nations.jpg', 'AVAILABLE', 'admin', 'admin'),

-- 创业投资
('精益创业', '埃里克·莱斯', '9787508638508', 4, 2, 4, 2, 2, 2012, '创业方法论经典，提出了最小可行产品和客户开发等重要概念。', '/images/books/lean-startup.jpg', 'AVAILABLE', 'admin', 'admin');

-- =============================================
-- 4. 插入社会科学类图书测试数据
-- =============================================

INSERT INTO books (book_title, book_author, isbn, category_id, region_id, total_quantity, available_quantity, borrowed_quantity, publication_year, book_description, cover_image_url, book_status, created_by, last_updated_by) VALUES
-- 心理学
('心理学与生活', '理查德·格里格', '9787115111314', 3, 1, 7, 5, 2, 2003, '心理学经典教材，将心理学理论与日常生活紧密结合。', '/images/books/psychology-and-life.jpg', 'AVAILABLE', 'admin', 'admin'),
('影响力', '罗伯特·西奥迪尼', '9787508614014', 3, 2, 5, 3, 2, 2010, '社会心理学经典著作，揭示了影响和说服他人的六大原则。', '/images/books/influence.jpg', 'AVAILABLE', 'admin', 'admin'),

-- 社会学
('社会学的想象力', 'C.赖特·米尔斯', '9787108035479', 3, 1, 3, 2, 1, 2017, '社会学经典著作，启发人们用社会学的视角思考个人与社会的关系。', '/images/books/sociological-imagination.jpg', 'AVAILABLE', 'admin', 'admin');

-- =============================================
-- 5. 插入历史地理类图书测试数据
-- =============================================

INSERT INTO books (book_title, book_author, isbn, category_id, region_id, total_quantity, available_quantity, borrowed_quantity, publication_year, book_description, cover_image_url, book_status, created_by, last_updated_by) VALUES
-- 中国历史
('史记', '司马迁', '9787101003048', 5, 1, 6, 4, 2, 2013, '中国历史学之父司马迁的传世名作，记录了从黄帝到汉武帝的历史。', '/images/books/records-of-grand-historian.jpg', 'AVAILABLE', 'admin', 'admin'),
('明朝那些事儿', '当年明月', '9787506344814', 5, 2, 8, 5, 3, 2009, '以生动幽默的语言讲述明朝历史，深受读者喜爱的历史读物。', '/images/books/those-things-in-ming-dynasty.jpg', 'AVAILABLE', 'admin', 'admin'),

-- 世界历史
('全球通史', '斯塔夫里阿诺斯', '9787301156377', 5, 1, 4, 3, 1, 2006, '从全球视角审视人类历史的发展进程，史学经典著作。', '/images/books/global-history.jpg', 'AVAILABLE', 'admin', 'admin');

-- =============================================
-- 6. 插入教育心理类图书测试数据
-- =============================================

INSERT INTO books (book_title, book_author, isbn, category_id, region_id, total_quantity, available_quantity, borrowed_quantity, publication_year, book_description, cover_image_url, book_status, created_by, last_updated_by) VALUES
-- 教育学
('教育学原理', '柳海民', '9787040298932', 7, 1, 5, 3, 2, 2010, '教育学专业核心教材，系统阐述了教育学的基本原理和方法。', '/images/books/principles-of-education.jpg', 'AVAILABLE', 'admin', 'admin'),
('如何阅读一本书', '莫提默·艾德勒', '9787100040990', 7, 2, 6, 4, 2, 2004, '阅读方法论经典，教授如何进行有效的阅读和思考。', '/images/books/how-to-read-a-book.jpg', 'AVAILABLE', 'admin', 'admin');

-- =============================================
-- 7. 插入医学健康类图书测试数据
-- =============================================

INSERT INTO books (book_title, book_author, isbn, category_id, region_id, total_quantity, available_quantity, borrowed_quantity, publication_year, book_description, cover_image_url, book_status, created_by, last_updated_by) VALUES
-- 健康养生
('黄帝内经', '佚名', '9787117118965', 6, 1, 4, 3, 1, 2009, '中医学经典著作，奠定了中医理论基础，养生保健指南。', '/images/books/huangdi-neijing.jpg', 'AVAILABLE', 'admin', 'admin'),
('众病之王：癌症传', '悉达多·穆克吉', '9787508628141', 6, 3, 3, 2, 1, 2012, '普利策奖获奖作品，全面介绍癌症的历史、现状和治疗。', '/images/books/emperor-of-all-maladies.jpg', 'AVAILABLE', 'admin', 'admin');

-- =============================================
-- 8. 插入工具参考类图书测试数据
-- =============================================

INSERT INTO books (book_title, book_author, isbn, category_id, region_id, total_quantity, available_quantity, borrowed_quantity, publication_year, book_description, cover_image_url, book_status, created_by, last_updated_by) VALUES
-- 工具书
('现代汉语词典', '中国社会科学院语言研究所', '9787100040884', 8, 1, 10, 8, 2, 2012, '汉语语文工具书，收录现代汉语词汇，是学习和工作的重要参考。', '/images/books/modern-chinese-dictionary.jpg', 'AVAILABLE', 'admin', 'admin'),
('牛津高阶英汉双解词典', 'A S Hornby', '9787100020615', 8, 2, 8, 6, 2, 2009, '英语学习权威词典，英汉双解，适合各级英语学习者。', '/images/books/oxford-advanced-dictionary.jpg', 'AVAILABLE', 'admin', 'admin'),
('API设计最佳实践', '刘韬', '9787121387654', 8, 3, 3, 2, 1, 2020, 'API设计参考手册，提供实用的API设计原则和最佳实践。', '/images/books/api-design-best-practices.jpg', 'AVAILABLE', 'admin', 'admin');

-- =============================================
-- 9. 插入特殊状态的测试数据
-- =============================================

-- 已损坏的图书
INSERT INTO books (book_title, book_author, isbn, category_id, region_id, total_quantity, available_quantity, borrowed_quantity, publication_year, book_description, cover_image_url, book_status, created_by, last_updated_by) VALUES
('计算机网络（已损坏）', '安德鲁·塔南鲍姆', '9787302123453', 2, 1, 2, 0, 0, 2011, '计算机网络经典教材，因为水浸损坏暂时不可借阅。', '/images/books/computer-networks-damaged.jpg', 'DAMAGED', 'admin', 'admin');

-- 已遗失的图书
INSERT INTO books (book_title, book_author, isbn, category_id, region_id, total_quantity, available_quantity, borrowed_quantity, publication_year, book_description, cover_image_url, book_status, created_by, last_updated_by) VALUES
('深入理解计算机系统（已遗失）', 'Randal E.Bryant', '9787111321312', 2, 2, 1, 0, 0, 2016, '计算机系统经典教材，已遗失正在补充采购中。', '/images/books/computer-systems-lost.jpg', 'LOST', 'admin', 'admin');

-- 不可借阅的图书
INSERT INTO books (book_title, book_author, isbn, category_id, region_id, total_quantity, available_quantity, borrowed_quantity, publication_year, book_description, cover_image_url, book_status, created_by, last_updated_by) VALUES
('图书馆管理手册（内部资料）', '图书馆管理委员会', '9787999999999', 8, 1, 1, 0, 0, 2023, '图书馆内部管理手册，仅供工作人员参考，不对外借阅。', '/images/books/library-manual-internal.jpg', 'UNAVAILABLE', 'admin', 'admin');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 10. 验证测试数据
-- =============================================

-- 按分类统计图书数量
SELECT '=== 按分类统计图书数量 ===' AS info;
SELECT 
    c.category_name AS '分类名称',
    COUNT(b.oid) AS '图书数量',
    SUM(b.total_quantity) AS '总册数',
    SUM(b.available_quantity) AS '可借册数',
    SUM(b.borrowed_quantity) AS '已借册数'
FROM categories c
LEFT JOIN books b ON c.oid = b.category_id AND b.enable_flag = 'T'
WHERE c.enable_flag = 'T'
GROUP BY c.oid, c.category_name
ORDER BY c.sort_order;

-- 按地区统计图书数量
SELECT '=== 按地区统计图书数量 ===' AS info;
SELECT 
    r.region_name AS '地区名称',
    COUNT(b.oid) AS '图书数量',
    SUM(b.total_quantity) AS '总册数',
    SUM(b.available_quantity) AS '可借册数',
    SUM(b.borrowed_quantity) AS '已借册数'
FROM regions r
LEFT JOIN books b ON r.oid = b.region_id AND b.enable_flag = 'T'
WHERE r.enable_flag = 'T'
GROUP BY r.oid, r.region_name
ORDER BY r.sort_order;

-- 按状态统计图书数量
SELECT '=== 按状态统计图书数量 ===' AS info;
SELECT 
    book_status AS '图书状态',
    COUNT(*) AS '图书数量',
    SUM(total_quantity) AS '总册数'
FROM books 
WHERE enable_flag = 'T'
GROUP BY book_status
ORDER BY COUNT(*) DESC;

-- 显示测试数据创建完成信息
SELECT CONCAT('测试数据创建完成! 总图书数: ', 
    (SELECT COUNT(*) FROM books WHERE enable_flag = 'T'), 
    '本, 总册数: ', 
    (SELECT SUM(total_quantity) FROM books WHERE enable_flag = 'T'), 
    '册') AS message;
